# FormItem

Form field decorator. It inherits the drag and drop functionality from [BlockItem](/components/block-item) and the rendering capabilities from [SchemaToolbar](/core/ui-schema/schema-toolbar) and [SchemaSettings](/core/ui-schema/schema-settings).

```ts
import { IFormItemProps } from '@formily/antd-v5';
type FormItemProps = IFormItemProps
```

## Example

<code src="./demos/new-demos/basic.tsx"></code>
