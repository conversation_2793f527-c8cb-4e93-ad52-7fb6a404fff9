

export default [
  {
    key: '17zb9wfq0vt',
    name: 'product',
    title: 's',
    inherit: false,
    fields: [],
    createdBy: true,
    updatedBy: true,
    sortable: true,
    template: 'general',
  },
  {
    key: '1s4aq7jbxdh',
    name: 'roles',
    title: '{{t("Roles")}}',
    template: 'general',
    inherit: false,
    fields: [
      {
        key: 'gs6190e27z5',
        name: 'name',
        type: 'uid',
        interface: 'input',
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'vy6i30jm9jm',
        uiSchema: {
          'x-uid': 'vy6i30jm9jm',
          name: 'vlldvknkn3t',
          type: 'string',
          title: '{{t("Role UID")}}',
          'x-component': 'Input',
        },
        prefix: 'r_',
        primaryKey: true,
      },
      {
        key: 'w0hvovoxnjh',
        name: 'title',
        type: 'string',
        interface: 'input',
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'pyq24tp0zkj',
        uiSchema: {
          'x-uid': 'pyq24tp0zkj',
          name: 'wtku9z07h69',
          type: 'string',
          title: '{{t("Role name")}}',
          'x-component': 'Input',
        },
        unique: true,
      },
      {
        key: 'lxysmomsdhy',
        name: 'description',
        type: 'string',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
      },
      {
        key: 'ynyhe4kgxku',
        name: 'strategy',
        type: 'json',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
      },
      {
        key: 'qr9949ps9dv',
        name: 'default',
        type: 'boolean',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        defaultValue: false,
      },
      {
        key: '11ts4nwrm8c',
        name: 'allowConfigure',
        type: 'boolean',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
      },
      {
        key: 'nxj4s7xev9g',
        name: 'allowNewMenu',
        type: 'boolean',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
      },
      {
        key: 'ibwisd53xeg',
        name: 'menuUiSchemas',
        type: 'belongsToMany',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        target: 'uiSchemas',
        targetKey: 'x-uid',
        foreignKey: 'roleName',
        sourceKey: 'name',
        otherKey: 'uiSchemaXUid',
        through: 'rolesUischemas',
      },
      {
        key: '0vt1uoy8f47',
        name: 'resources',
        type: 'hasMany',
        interface: null,
        collectionName: 'roles',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        target: 'rolesResources',
        sourceKey: 'name',
        targetKey: 'id',
        foreignKey: 'roleName',
      },
    ],
    autoGenId: false,
    model: 'RoleModel',
    filterTargetKey: 'name',
  },
  {
    key: 'nhng5sgypw8',
    name: 't_j6omof6tza8',
    title: '任务',
    template: 'general',
    inherit: false,
    fields: [
      {
        key: 'aabs1ya9aux',
        name: 'f_g8j5jvalqh0',
        type: 'string',
        interface: 'input',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'tzb6h0tsbis',
        uiSchema: {
          'x-uid': 'tzb6h0tsbis',
          name: 'y94c05v2hp2',
          type: 'string',
          'x-component': 'Input',
          title: '名称',
          enum: [],
        },
      },
      {
        key: 'kidn54kisne',
        name: 'f_tegyd222bcc',
        type: 'text',
        interface: 'textarea',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'p4uucjm3rrx',
        uiSchema: {
          'x-uid': 'p4uucjm3rrx',
          name: 'ri9ulk87owm',
          type: 'string',
          'x-component': 'Input.TextArea',
          title: '描述',
          enum: [],
        },
      },
      {
        key: 'kwdiruosgsb',
        name: 'f_u007sq2jg93',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: 's5w3l6pl1hn',
        uiSchemaUid: 'iea79k3kmao',
        uiSchema: {
          'x-uid': 'iea79k3kmao',
          name: 'njked4wch3i',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'id', value: 'id' } },
          title: '负责人',
          enum: [],
        },
        target: 'users',
        targetKey: 'id',
        sourceKey: 'id',
        through: 't_7476m1wwsfe',
        foreignKey: 'f_9zwkbo7dd18',
        otherKey: 'f_q84aphtx7eb',
      },
      {
        key: 'w7civak6gp3',
        name: 'f_hpmvdltzs6m',
        type: 'string',
        interface: 'radioGroup',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '481uv9zj072',
        uiSchema: {
          'x-uid': '481uv9zj072',
          name: '8fyelcfuzfu',
          enum: [
            { value: '67snt3e6yld', label: '未开始', color: 'default' },
            { value: 'ht963n365al', label: '进行中', color: 'green' },
            { value: '2s2s8wzcnm0', label: '测试中', color: 'volcano' },
            { value: 'irepvdzp1ac', label: '已完成', color: 'blue' },
          ],
          type: 'string',
          'x-component': 'Radio.Group',
          title: '状态',
        },
      },
      {
        key: '9x87w5z0ea6',
        name: 'f_jj9cyhron1d',
        type: 'hasMany',
        interface: 'subTable',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'pndvx008mmn',
        uiSchema: {
          'x-uid': 'pndvx008mmn',
          name: 'l8xqzpkxj3p',
          type: 'array',
          'x-component': 'Table',
          'x-component-props': {},
          enum: [],
          title: '子任务',
        },
        targetKey: 'id',
        sourceKey: 'id',
        foreignKey: 'f_yzivojrp6l8',
        target: 't_ab12qiwruwk',
      },
      {
        key: '3f4y1iq16ux',
        name: 'f_ooar0pto2ko',
        type: 'belongsToMany',
        interface: 'attachment',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '5xf7izxjny5',
        uiSchema: {
          'x-uid': '5xf7izxjny5',
          name: '9az9003ijcm',
          'x-component-props': { multiple: true, action: 'attachments:create' },
          type: 'array',
          'x-component': 'Upload.Attachment',
          title: '附件',
          enum: [],
        },
        target: 'attachments',
        targetKey: 'id',
        sourceKey: 'id',
        through: 't_5a7wh2qhk4l',
        foreignKey: 'f_tqfsxclrexu',
        otherKey: 'f_j2krlyq6oka',
      },
      {
        key: '4um5urxskg0',
        name: 'f_f7txg1oc3nt',
        type: 'date',
        interface: 'createdAt',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'jjax6r7c84m',
        uiSchema: {
          'x-uid': 'jjax6r7c84m',
          name: 'clidfmw96u3',
          'x-component-props': { dateFormat: 'YYYY-MM-DD', showTime: true, timeFormat: 'HH:mm:ss' },
          type: 'datetime',
          title: '创建时间',
          'x-component': 'DatePicker',
          'x-read-pretty': true,
          enum: [],
        },
        field: 'createdAt',
      },
      {
        key: 'dnwqg2s3oph',
        name: 'f_2dpc76bszit',
        type: 'belongsTo',
        interface: 'createdBy',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'po3y2pc112z',
        uiSchema: {
          'x-uid': 'po3y2pc112z',
          name: 'ke2rte2zikm',
          type: 'object',
          title: '创建人',
          'x-component': 'RecordPicker',
          'x-component-props': { fieldNames: { value: 'id', label: 'nickname' } },
          'x-read-pretty': true,
          enum: [],
        },
        target: 'users',
        foreignKey: 'createdById',
        targetKey: 'id',
      },
      {
        key: '1sayjktagk3',
        name: 'f_z27302tl2bf',
        type: 'string',
        interface: 'select',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '0a28rxnov8o',
        uiSchema: {
          'x-uid': '0a28rxnov8o',
          name: '2vg44ojdq4j',
          enum: [
            { value: 'mkvgt7ndsqd', label: '缺陷', color: 'volcano' },
            { value: '9yyv2tawmry', label: '交互', color: 'green' },
            { value: 'vx4bhmtsuus', label: '需求', color: 'cyan' },
          ],
          type: 'string',
          'x-component': 'Select',
          title: '类型',
        },
      },
      {
        key: 'zhvtzwta7bz',
        name: 'f_yc8jbfiqfvh',
        type: 'string',
        interface: 'radioGroup',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'jijqs397op9',
        uiSchema: {
          'x-uid': 'jijqs397op9',
          name: '99v9hnujiuh',
          enum: [
            { value: 's7hocw6twfk', label: '高', color: 'red' },
            { value: 'v25527dxseu', label: '中', color: 'blue' },
            { value: 'c4hobfb5k07', label: '低', color: 'lime' },
          ],
          type: 'string',
          'x-component': 'Radio.Group',
          title: '优先级',
        },
      },
      {
        key: 'nobsmkp6ypg',
        name: 'f_ksgzy9vmgce',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: 'z70jtfabrm7',
        uiSchemaUid: '3gbviwfd0cv',
        uiSchema: {
          'x-uid': '3gbviwfd0cv',
          name: '6sabm3win42',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: false, fieldNames: { label: 'id', value: 'id' } },
          title: '迭代',
          enum: [],
        },
        target: 't_94rsj6kbzvn',
        targetKey: 'id',
        sourceKey: 'id',
        through: 't_ewdf2dsjlu3',
        foreignKey: 'f_72tnpdz6kfg',
        otherKey: 'f_8do8o1wsavk',
      },
      {
        key: 'rwtqeecnbst',
        name: 'f_zek99qhv0vc',
        type: 'date',
        interface: 'updatedAt',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'df0kmwka9ra',
        uiSchema: {
          'x-uid': 'df0kmwka9ra',
          name: 'e3hmz8upyle',
          'x-component-props': { dateFormat: 'YYYY-MM-DD', showTime: true, timeFormat: 'HH:mm:ss' },
          type: 'datetime',
          title: '更新时间',
          'x-component': 'DatePicker',
          'x-read-pretty': true,
        },
        field: 'updatedAt',
      },
      {
        key: 'tm66hbii6zz',
        name: 'f_cht6rsiiiko',
        type: 'string',
        interface: 'select',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'tkfjlde4ppl',
        uiSchema: {
          'x-uid': 'tkfjlde4ppl',
          name: 'p0rccyurzle',
          enum: [
            { value: 'uduxfqpr5t2', label: '菜单' },
            { value: 'dnphxkwat35', label: '权限' },
            { value: '5cso9lpizog', label: '数据表' },
            { value: 'wlg329nsu3a', label: '字段' },
            { value: 'm8z7r06vk9z', label: '表格' },
            { value: 'ftfccbha25a', label: '表单' },
            { value: 'o33ljfkje2h', label: '详情' },
            { value: 'ym9wwk3aqbu', label: '日历' },
            { value: 'wge57qtiz14', label: '看板' },
            { value: 'm4xoc8rbafg', label: '操作' },
            { value: 'r6l2wg1snxc', label: '配置' },
            { value: 'j1cv1vf16fm', label: '附件' },
            { value: 'l464jdsy7vr', label: '容器' },
          ],
          type: 'string',
          'x-component': 'Select',
          title: '分类',
        },
      },
      {
        key: 'c9hzonl1hzq',
        name: 'f_47f2d9wgofm',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: '7mufig9muag',
        uiSchemaUid: 'rbl27ngs563',
        uiSchema: {
          'x-uid': 'rbl27ngs563',
          name: 'xrvwttiv064',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'id', value: 'id' } },
          title: '相关任务',
        },
        target: 't_j6omof6tza8',
        targetKey: 'id',
        sourceKey: 'id',
        through: 't_qhgbwomd2t7',
        foreignKey: 'f_jes256zqwr1',
        otherKey: 'f_gtgjj059mye',
      },
      {
        key: '7mufig9muag',
        name: 'f_qmlomqm7lvb',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: 'c9hzonl1hzq',
        uiSchemaUid: 'vgy9cgq9v6b',
        uiSchema: {
          'x-uid': 'vgy9cgq9v6b',
          name: 'sf99arn65jj',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'id', value: 'id' } },
          title: '任务',
        },
        target: 't_j6omof6tza8',
        through: 't_qhgbwomd2t7',
        sourceKey: 'id',
        foreignKey: 'f_gtgjj059mye',
        targetKey: 'id',
        otherKey: 'f_jes256zqwr1',
      },
      {
        key: 'hv3kx2z8edn',
        name: 'f_hpmvdltzs6m_sort',
        type: 'sort',
        interface: null,
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        hidden: true,
        scopeKey: 'f_hpmvdltzs6m',
      },
      {
        key: 'q6wy6jcpdfr',
        name: 'f_a4z4h45vi5b',
        type: 'string',
        interface: 'select',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'lx4k023vm7k',
        uiSchema: {
          'x-uid': 'lx4k023vm7k',
          name: 'e9u95lm0c2q',
          enum: [
            { value: 'hgobsqbfnd4', label: 'SchemaInitializer', color: 'red' },
            { value: 't8prrpfk7kl', label: 'SchemaSettings', color: 'magenta' },
            { value: 'r3fxlrunwd8', label: 'SchemaComponent', color: 'lime' },
            { value: 'bidogljo0l3', label: 'CollectionField', color: 'blue' },
            { value: 'ygy1zfdr1z6', label: 'ACL', color: 'purple' },
            { value: 'ef9qf0wzpt5', label: 'DndContext', color: 'cyan' },
            { value: '45sfwd7ie30', label: 'useRequest', color: 'volcano' },
          ],
          type: 'string',
          'x-component': 'Select',
          title: '开发分类',
        },
      },
      {
        key: '936iis76zdx',
        name: 'f_a4z4h45vi5b_sort',
        type: 'sort',
        interface: null,
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        hidden: true,
        scopeKey: 'f_a4z4h45vi5b',
      },
      {
        key: 'zre21o0a5a9',
        name: 'f_d93g4r08krl',
        type: 'string',
        interface: 'input',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '96geljdh9j6',
        uiSchema: {
          'x-uid': '96geljdh9j6',
          name: 'zplxa37mb0y',
          type: 'string',
          'x-component': 'Input',
          title: '评估',
          enum: [],
        },
      },
      {
        key: 'jasdf0kcqbs',
        name: 'f_1ckuegfab9s',
        type: 'text',
        interface: 'textarea',
        collectionName: 't_j6omof6tza8',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '61h9pserdqr',
        uiSchema: {
          'x-uid': '61h9pserdqr',
          name: 'uepfpdv289y',
          type: 'string',
          'x-component': 'Input.TextArea',
          title: '评估备注',
        },
      },
    ],
    createdBy: true,
    updatedBy: true,
    sortable: true,
  },
  {
    key: 'nlxapox70a3',
    name: 't_ab12qiwruwk',
    template: 'general',
    title: null,
    inherit: true,
    fields: [
      {
        key: 'q9xx9ixxxyb',
        name: 'f_m7ibo1vrvnm',
        type: 'string',
        interface: 'input',
        collectionName: 't_ab12qiwruwk',
        parentKey: '9x87w5z0ea6',
        reverseKey: null,
        uiSchemaUid: 'ucn6wm03zrq',
        uiSchema: {
          'x-uid': 'ucn6wm03zrq',
          name: 'k07s9nhhj6c',
          type: 'string',
          'x-component': 'Input',
          title: '名称',
        },
      },
      {
        key: 'y91qig4eyzm',
        name: 'f_kukaw9kddyj',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 't_ab12qiwruwk',
        parentKey: '9x87w5z0ea6',
        reverseKey: 'tvwwt24vukv',
        uiSchemaUid: '7c9ch1fknbr',
        uiSchema: {
          'x-uid': '7c9ch1fknbr',
          name: 'gv1k2nhucte',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: false, fieldNames: { label: 'id', value: 'id' } },
          title: '负责人',
        },
        target: 'users',
        targetKey: 'id',
        sourceKey: 'id',
        through: 't_gsv43y8ebt8',
        foreignKey: 'f_hbbvlsbe9gs',
        otherKey: 'f_bhj1xy7uyc8',
      },
      {
        key: 'gg0xx1vln6w',
        name: 'f_4mpiovytw4d',
        type: 'text',
        interface: 'textarea',
        collectionName: 't_ab12qiwruwk',
        parentKey: '9x87w5z0ea6',
        reverseKey: null,
        uiSchemaUid: '3xgmf0ee5fn',
        uiSchema: {
          'x-uid': '3xgmf0ee5fn',
          name: '28sf67whpfs',
          type: 'string',
          'x-component': 'Input.TextArea',
          title: '描述',
        },
      },
      {
        key: 'zpz6o74z9u4',
        name: 'f_lxsum89wkzd',
        type: 'belongsToMany',
        interface: 'attachment',
        collectionName: 't_ab12qiwruwk',
        parentKey: '9x87w5z0ea6',
        reverseKey: null,
        uiSchemaUid: 'wosew16td91',
        uiSchema: {
          'x-uid': 'wosew16td91',
          name: 'p82ihvtkxtf',
          'x-component-props': { multiple: true, action: 'attachments:create' },
          type: 'array',
          'x-component': 'Upload.Attachment',
          title: '附件',
        },
        target: 'attachments',
        targetKey: 'id',
        sourceKey: 'id',
        through: 't_cn6cweinuw7',
        foreignKey: 'f_r824sp05l43',
        otherKey: 'f_8k3e4i0d04y',
      },
    ],
    createdBy: true,
    updatedBy: true,
    sortable: true,
  },
  {
    key: 'oftbzt8nm2o',
    name: 'users',
    title: '{{t("Users")}}',
    template: 'general',
    inherit: false,
    fields: [
      {
        key: 'khov1egnsur',
        name: 'nickname',
        type: 'string',
        interface: 'input',
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '3ohfk46bf7o',
        uiSchema: {
          'x-uid': '3ohfk46bf7o',
          name: 'prmmf3kg8rx',
          type: 'string',
          title: '{{t("Nickname")}}',
          'x-component': 'Input',
        },
      },
      {
        key: 'u6cvmn45o6a',
        name: 'email',
        type: 'string',
        interface: 'email',
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '69ywpwux1zh',
        uiSchema: {
          'x-uid': '69ywpwux1zh',
          name: 'u8djuprwttj',
          type: 'string',
          title: '{{t("Email")}}',
          'x-component': 'Input',
          require: true,
        },
        unique: true,
      },
      {
        key: 'jwaky9982f2',
        name: 'password',
        type: 'password',
        interface: 'password',
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'qs374baqkh0',
        uiSchema: {
          'x-uid': 'qs374baqkh0',
          name: 'tf5dolxh2i4',
          type: 'string',
          title: '{{t("Password")}}',
          'x-component': 'Password',
        },
        hidden: true,
      },
      {
        key: '4g5nics20go',
        name: 'roles',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'lpkacakd4my',
        uiSchema: {
          'x-uid': 'lpkacakd4my',
          name: '3oylvillmwu',
          type: 'array',
          title: '{{t("Roles")}}',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'title', value: 'name' } },
        },
        target: 'roles',
        foreignKey: 'userId',
        otherKey: 'roleName',
        sourceKey: 'id',
        targetKey: 'name',
        through: 'rolesUsers',
      },
      {
        key: 'cqqhyq32tvf',
        name: 'appLang',
        type: 'string',
        interface: null,
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
      },
      {
        key: 't7t97s1rwid',
        name: 'token',
        type: 'string',
        interface: null,
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        unique: true,
        hidden: true,
      },
      {
        key: '5myliungho1',
        name: 'resetToken',
        type: 'string',
        interface: null,
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        unique: true,
        hidden: true,
      },
      {
        key: 'q2eg83quxls',
        name: 'sort',
        type: 'sort',
        interface: null,
        collectionName: 'users',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: null,
        uiSchema: {},
        hidden: true,
      },
      {
        key: 's5w3l6pl1hn',
        name: 'f_xd4v2uljqcu',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 'users',
        parentKey: null,
        reverseKey: 'kwdiruosgsb',
        uiSchemaUid: 'zv4gvihowwm',
        uiSchema: {
          'x-uid': 'zv4gvihowwm',
          name: 'mjjpnphkkhf',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'id', value: 'id' } },
          title: '负责人',
        },
        target: 't_j6omof6tza8',
        through: 't_7476m1wwsfe',
        sourceKey: 'id',
        foreignKey: 'f_q84aphtx7eb',
        targetKey: 'id',
        otherKey: 'f_9zwkbo7dd18',
      },
      {
        key: 'tvwwt24vukv',
        name: 'f_qpsqy5rtc8t',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 'users',
        parentKey: null,
        reverseKey: 'y91qig4eyzm',
        uiSchemaUid: '5ksz03zw47c',
        uiSchema: {
          'x-uid': '5ksz03zw47c',
          name: '8ukjiawzduz',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'id', value: 'id' } },
          title: '负责人',
        },
        target: 't_ab12qiwruwk',
        through: 't_gsv43y8ebt8',
        sourceKey: 'id',
        foreignKey: 'f_bhj1xy7uyc8',
        targetKey: 'id',
        otherKey: 'f_hbbvlsbe9gs',
      },
    ],
    sortable: 'sort',
  },
  {
    key: 'tyztw0ulriv',
    name: 't_94rsj6kbzvn',
    title: '迭代',
    template: 'general',
    inherit: false,
    fields: [
      {
        key: 'nq4mp00jwip',
        name: 'f_zio9ewkxss7',
        type: 'string',
        interface: 'input',
        collectionName: 't_94rsj6kbzvn',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'q7vois7ptlt',
        uiSchema: {
          'x-uid': 'q7vois7ptlt',
          name: 'o9ue1s4tu8k',
          type: 'string',
          'x-component': 'Input',
          title: '名称',
          enum: [],
        },
      },
      {
        key: 'ec2833af7n1',
        name: 'f_ojboh2wxpju',
        type: 'text',
        interface: 'textarea',
        collectionName: 't_94rsj6kbzvn',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'pco1f8sq2jq',
        uiSchema: {
          'x-uid': 'pco1f8sq2jq',
          name: 'lb2hdydextn',
          type: 'string',
          'x-component': 'Input.TextArea',
          title: '描述',
          enum: [],
        },
      },
      {
        key: '82uqmdjr3v4',
        name: 'f_nunmzapigvk',
        type: 'date',
        interface: 'datetime',
        collectionName: 't_94rsj6kbzvn',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '2gawk8arctt',
        uiSchema: {
          'x-uid': '2gawk8arctt',
          name: 'j9j7c2wyev2',
          'x-component-props': { dateFormat: 'YYYY-MM-DD', showTime: false },
          type: 'datetime',
          'x-component': 'DatePicker',
          title: '开始日期',
          enum: [],
        },
      },
      {
        key: 'mdx2eqz9ei8',
        name: 'f_rberbnphu9u',
        type: 'date',
        interface: 'datetime',
        collectionName: 't_94rsj6kbzvn',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'pj0znpff63y',
        uiSchema: {
          'x-uid': 'pj0znpff63y',
          name: 'c5h7agr2qy4',
          'x-component-props': { dateFormat: 'YYYY-MM-DD', showTime: false },
          type: 'datetime',
          'x-component': 'DatePicker',
          title: '结束日期',
          enum: [],
        },
      },
      {
        key: 'z70jtfabrm7',
        name: 'f_rabhmdetc3p',
        type: 'belongsToMany',
        interface: 'linkTo',
        collectionName: 't_94rsj6kbzvn',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'aun2xsfua8d',
        uiSchema: {
          'x-uid': 'aun2xsfua8d',
          name: 'i508c1outz2',
          'x-component': 'RecordPicker',
          'x-component-props': { multiple: true, fieldNames: { label: 'id', value: 'id' } },
          title: '任务',
          enum: [],
        },
        target: 't_j6omof6tza8',
        through: 't_ewdf2dsjlu3',
        sourceKey: 'id',
        foreignKey: 'f_8do8o1wsavk',
        targetKey: 'id',
        otherKey: 'f_72tnpdz6kfg',
      },
    ],
    createdBy: true,
    updatedBy: true,
    sortable: true,
  },
  {
    key: 'vv8umfa9592',
    template: 'general',
    name: 'test_sheet',
    title: '测试表',
    inherit: false,
    fields: [
      {
        key: 'q1xye1ooeol',
        name: 'f_7mh6k6re0ey',
        type: 'string',
        interface: 'phone',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 't9ddc5n5pv8',
        uiSchema: {
          'x-uid': 't9ddc5n5pv8',
          name: 'd3hwsg020u8',
          type: 'string',
          'x-component': 'Input',
          'x-validator': 'phone',
          title: '手机',
        },
      },
      {
        key: '25icwn35oin',
        name: 'f_turkjlzi52z',
        type: 'string',
        interface: 'email',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '3bkgd97f63v',
        uiSchema: {
          'x-uid': '3bkgd97f63v',
          name: 'zkvhu5cm4no',
          type: 'string',
          'x-component': 'Input',
          'x-validator': 'email',
          title: '邮箱',
        },
      },
      {
        key: '3cqe0mpcrmg',
        name: 'f_c14whhblhtd',
        type: 'float',
        interface: 'number',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '4bajzziz060',
        uiSchema: {
          'x-uid': '4bajzziz060',
          name: '366t9rvl34b',
          'x-component-props': { step: '0.01', stringMode: true },
          type: 'number',
          'x-component': 'InputNumber',
          title: '数字',
        },
      },
      {
        key: 'y39vi1z69pw',
        name: 'f_z1b674d907e',
        type: 'float',
        interface: 'percent',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'vbnoib14uzl',
        uiSchema: {
          'x-uid': 'vbnoib14uzl',
          name: 'rumohzj4lh6',
          'x-component-props': { step: '0.1', stringMode: true, addonAfter: '%' },
          type: 'string',
          'x-component': 'InputNumber',
          title: '百分比',
        },
      },
      {
        key: 'eouuwoag6ka',
        name: 'f_4tzp4nhmvt2',
        type: 'string',
        interface: 'icon',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'qmmw3zc8l60',
        uiSchema: {
          'x-uid': 'qmmw3zc8l60',
          name: 'aivg5c51gc9',
          type: 'string',
          'x-component': 'IconPicker',
          title: '图标',
        },
      },
      {
        key: 'qurfmfe2hfi',
        name: 'f_z0s4m3hgadd',
        type: 'password',
        interface: 'password',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'tkp46kn682m',
        uiSchema: {
          'x-uid': 'tkp46kn682m',
          name: '7q7yeobot0j',
          type: 'string',
          'x-component': 'Password',
          title: '密码',
        },
      },
      {
        key: 'ka4k5tfh3k5',
        name: 'f_q5qnozezx9s',
        type: 'boolean',
        interface: 'checkbox',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'wgyf4ozsvdi',
        uiSchema: {
          'x-uid': 'wgyf4ozsvdi',
          name: 'x6agce8s18w',
          type: 'boolean',
          'x-component': 'Checkbox',
          title: '勾选',
        },
      },
      {
        key: '3dhczyw3ye2',
        name: 'f_jbzwvldpyaa',
        type: 'array',
        interface: 'multipleSelect',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'hlmfjs2pu9t',
        uiSchema: {
          'x-uid': 'hlmfjs2pu9t',
          name: 'ao52nx5pune',
          enum: [
            { value: '6mbqdwuzfca', label: '选项1', color: 'red' },
            { value: '9zi6s2fbj0q', label: '选项2', color: 'geekblue' },
          ],
          type: 'array',
          'x-component': 'Select',
          'x-component-props': { mode: 'multiple' },
          title: '多选',
        },
        defaultValue: [],
      },
      {
        key: 'erdrp1gi587',
        name: 'f_04krym08wxq',
        type: 'array',
        interface: 'checkboxGroup',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '6has8wyzohq',
        uiSchema: {
          'x-uid': '6has8wyzohq',
          name: 'fvwsjt58xx4',
          enum: [
            { value: '4lsgdvfl7xq', label: '复选2', color: 'cyan' },
            { value: 'qzuki8bmilg', label: '复选1', color: 'purple' },
          ],
          type: 'string',
          'x-component': 'Checkbox.Group',
          title: '复选',
        },
        defaultValue: [],
      },
      {
        key: 'rykbub69xu7',
        name: 'f_xclbl3htmxs',
        type: 'belongsToMany',
        interface: 'chinaRegion',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'yml5yqzgvn8',
        uiSchema: {
          'x-uid': 'yml5yqzgvn8',
          name: '2ye7y09agup',
          'x-component-props': {
            maxLevel: 5,
            useDataSource: '{{ useChinaRegionDataSource }}',
            useLoadData: '{{ useChinaRegionLoadData }}',
            changeOnSelectLast: true,
            labelInValue: true,
            fieldNames: { label: 'name', value: 'code', children: 'children' },
          },
          type: 'array',
          'x-component': 'Cascader',
          title: '中国地区',
        },
        target: 'chinaRegions',
        targetKey: 'code',
        sortBy: 'level',
        sourceKey: 'id',
        through: 't_o9ry49u6xez',
        foreignKey: 'f_44r8nsvdvz7',
        otherKey: 'f_l0tzoymin3i',
      },
      {
        key: 'acm789iz922',
        name: 'f_v4cgx7l5hwm',
        type: 'text',
        interface: 'markdown',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '8a2erdhwcvb',
        uiSchema: {
          'x-uid': '8a2erdhwcvb',
          name: 'rqeiz26vuns',
          type: 'string',
          'x-component': 'Markdown',
          title: 'Markdown',
        },
      },
      {
        key: 'zmip3swglzv',
        name: 'f_ztaop411cdw',
        type: 'time',
        interface: 'time',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'r6s6rrjzfml',
        uiSchema: {
          'x-uid': 'r6s6rrjzfml',
          name: 'ct893ay5apv',
          'x-component-props': { format: 'HH:mm:ss' },
          type: 'string',
          'x-component': 'TimePicker',
          title: '时间',
        },
      },
      {
        key: 'nx1p69b6g96',
        name: 'f_mf8ccm0pjmh',
        type: 'date',
        interface: 'datetime',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: '08p9oztyjf2',
        uiSchema: {
          'x-uid': '08p9oztyjf2',
          name: 'bkdnndmfpeo',
          'x-component-props': { dateFormat: 'DD/MM/YYYY', showTime: true, timeFormat: 'HH:mm:ss' },
          type: 'datetime',
          'x-component': 'DatePicker',
          title: '日期',
          enum: [],
        },
      },
      {
        key: 'ge57kma4gue',
        name: 'f_dqq0xjkdqbo',
        type: 'belongsTo',
        interface: 'createdBy',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'zzmst1r01gc',
        uiSchema: {
          'x-uid': 'zzmst1r01gc',
          name: 'def1ngennlx',
          type: 'object',
          title: '创建人',
          'x-component': 'RecordPicker',
          'x-component-props': { fieldNames: { value: 'id', label: 'nickname' } },
          'x-read-pretty': true,
        },
        target: 'users',
        foreignKey: 'createdById',
        targetKey: 'id',
      },
      {
        key: 'yqye22n1f1c',
        name: 'f_7ukoar9jevs',
        type: 'date',
        interface: 'createdAt',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'lgnqb28lb91',
        uiSchema: {
          'x-uid': 'lgnqb28lb91',
          name: 'jdjz5cngm3j',
          'x-component-props': { dateFormat: 'YYYY-MM-DD', showTime: true, timeFormat: 'HH:mm:ss' },
          type: 'datetime',
          title: '创建日期',
          'x-component': 'DatePicker',
          'x-read-pretty': true,
        },
        field: 'createdAt',
      },
      {
        key: 'n4ap9l1l4wd',
        name: 'f_81orjgxlbuo',
        type: 'date',
        interface: 'updatedAt',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'z19egimonh9',
        uiSchema: {
          'x-uid': 'z19egimonh9',
          name: 'xtzu6cpnsm4',
          'x-component-props': { dateFormat: 'YYYY-MM-DD', showTime: true },
          type: 'datetime',
          title: '修改日期',
          'x-component': 'DatePicker',
          'x-read-pretty': true,
        },
        field: 'updatedAt',
      },
      {
        key: 'oqy67fm6af8',
        name: 'f_e31b9oqe9ej',
        type: 'belongsTo',
        interface: 'updatedBy',
        collectionName: 'test_sheet',
        parentKey: null,
        reverseKey: null,
        uiSchemaUid: 'tm4f7z0nyso',
        uiSchema: {
          'x-uid': 'tm4f7z0nyso',
          name: '19srjtwyce1',
          type: 'object',
          title: '修改人',
          'x-component': 'RecordPicker',
          'x-component-props': { fieldNames: { value: 'id', label: 'nickname' } },
          'x-read-pretty': true,
        },
        target: 'users',
        foreignKey: 'updatedById',
        targetKey: 'id',
      },
    ],
    createdBy: true,
    updatedBy: true,
    sortable: true,
  },
];
