/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export const pageSizeOptions = [12, 24, 60, 96, 120];
export const gridSizes = ['xs', 'md', 'lg', 'xxl'];
export const screenSizeTitleMaps = {
  xs: 'Phone device',
  md: 'Tablet device',
  lg: 'Desktop device',
  xxl: 'Large screen device',
};
export const screenSizeMaps = {
  xs: '< 768',
  md: '≥ 768',
  lg: '≥ 992',
  xxl: '≥ 1600',
};

export const defaultColumnCount = {
  xs: 1,
  md: 2,
  lg: 3,
  xxl: 4,
};
