# Form

## Examples

### Basic Usage

<code src="./demos/demo2.tsx"></code>

### Form Decorator

Form can also be used as a decorator

<code src="./demos/demo6.tsx"></code>

When combined with Action.Drawer, it becomes DrawerForm

<code src="./demos/demo1.tsx"></code>

### initialValue Initialization

<code src="./demos/demo3.tsx"></code>

### initialValue for decorator

<code src="./demos/demo4.tsx"></code>

### Remote initialization data

<code src="./demos/demo5.tsx"></code>

### useValues

<code src="./demos/demo7.tsx"></code>

### DrawerForm

Control the popup form (Drawer+Form) freely and asynchronously populate the form data

<code src="./demos/demo8.tsx"></code>

## API

Property Description

- `initialValue` Static initialization data
- `request` Remote request parameters
- `useValues` Custom useRequest
