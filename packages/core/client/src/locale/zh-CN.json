{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "每页显示 <1><0>10</0><1>20</1><2>50</2><3>100</3></1> 条", "Page number": "页码", "Page size": "每页条数", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "满足组内 <1><0>全部</0><1>任意</1></1> 条件", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "在 <1><0>对话框</0><1>抽屉</1><2>窗口</2></1> 内打开", "{{count}} filter items": "{{count}} 个筛选项", "{{count}} more items": "还有 {{count}} 项", "Total {{count}} items": "总共 {{count}} 条", "Today": "今天", "Yesterday": "昨天", "Tomorrow": "明天", "Month": "月", "Week": "周", "This week": "本周", "Next week": "下周", "This month": "本月", "Next month": "下月", "Last quarter": "上季度", "This quarter": "本季度", "Next quarter": "下季度", "This year": "今年", "Next year": "明年", "Last week": "上周", "Last month": "上月", "Last year": "去年", "Last 7 days": "最近 7 天", "Last 30 days": "最近 30 天", "Last 90 days": "最近 90 天", "Next 7 days": "未来 7 天", "Next 30 days": "未来 30 天", "Next 90 days": "未来 90 天", "Work week": "工作日", "Day": "天", "Agenda": "列表", "Date": "日期", "Time": "时间", "Event": "事件", "None": "无", "Unconnected": "未连接", "System settings": "系统设置", "System title": "系统名称", "Setting": "设置", "Settings": "设置", "Enable": "启用", "Disable": "禁用", "On": "启用", "Off": "禁用", "Logo": "Logo", "Add menu item": "添加菜单项", "Page": "页面", "Tab": "标签", "Name": "名称", "Icon": "图标", "Group": "分组", "Link": "链接", "Save conditions": "保存筛选条件", "Edit menu item": "编辑菜单项", "Move to": "移动到", "Insert left": "在左边插入", "Insert right": "在右边插入", "Insert inner": "在里面插入", "Delete": "删除", "Disassociate": "解除关联", "Disassociate record": "解除关联记录", "Are you sure you want to disassociate it?": "你确定要解除关联吗？", "UI editor": "界面配置", "Collection": "数据表", "Collection selector": "数据表选择器", "Providing certain collections as options for users, typically used in polymorphic or inheritance scenarios": "将数据表里的某些表作为可选项供给用户选择，一般用在多态或继承的场景里", "Enable child collections": "启用子表", "Allow adding records to the current collection": "允许向当前数据表里添加记录", "Collections & Fields": "数据表配置", "All collections": "全部数据表", "Add category": "添加分类", "Delete category": "删除分类", "Edit category": "编辑分类", "Collection category": "数据表类别", "Collection template": "数据表模板", "Visible": "显示", "Read only": "只读(禁止编辑)", "Easy reading": "只读(阅读模式)", "Hidden": "隐藏", "Hidden(reserved value)": "隐藏(保留值)", "Not required": "非必填", "Value": "值", "Disabled": "禁用", "Enabled": "启用", "Problematic": "有问题", "Empty": "赋空值", "Linkage rule": "联动规则", "Linkage rules": "联动规则", "Condition": "条件", "Properties": "属性", "Add linkage rule": "添加联动规则", "Add property": "添加属性", "Calculation engine": "计算引擎", "Expression": "表达式", "Expression collection": "表达式表", "Sort": "排序", "Categories": "数据表类别", "Category name": "分类名称", "Add child": "添加子记录", "Collapse all": "全部收起", "Expand all": "全部展开", "Expand/Collapse": "展开/折叠", "Default collapse": "默认展开", "Tree collection": "树结构表", "Tree table": "树表格", "Parent ID": "父记录ID", "Parent": "父记录", "Children": "子记录", "Roles & Permissions": "角色和权限", "Edit profile": "个人资料", "Change password": "修改密码", "Old password": "旧密码", "New password": "新密码", "Switch role": "切换角色", "Super admin": "超级管理员", "Language": "语言设置", "Allow sign up": "允许注册", "Enable SMS authentication": "启用短信登录和注册", "Sign out": "注销", "Cancel": "取消", "Confirm": "确定", "Submit": "提交", "Close": "关闭", "Set the data scope": "设置数据范围", "Data loading mode": "数据加载方式", "Set data loading mode": "设置数据加载方式", "Load all data when filter is empty": "筛选条件为空时加载全部数据", "Do not load data when filter is empty": "筛选条件为空时不加载数据", "Block": "区块", "Data blocks": "数据区块", "Filter blocks": "筛选区块", "Table": "表格", "Form": "表单", "List": "列表", "Grid Card": "网格卡片", "Screen size": "屏幕尺寸", "pixels": "像素", "Display title": "显示标题", "Set the count of columns displayed in a row": "设置一行展示的列数", "Column": "列", "Phone device": "手机设备", "Tablet device": "平板设备", "Desktop device": "电脑设备", "Large screen device": "大屏幕设备", "Table OID(Inheritance)": "数据表 OID(继承)", "Collapse": "折叠面板", "Select data source": "选择数据源", "Calendar": "日历", "Delete events": "删除日程", "This event": "此日程", "This and following events": "此日程及后续日程", "All events": "所有日程", "Delete this event?": "是否删除这个日程？", "Delete Event": "删除日程", "Kanban": "看板", "Gantt": "甘特图", "Create gantt block": "创建甘特图区块", "Progress field": "进度字段", "Time scale": "时间缩放等级", "Hour": "小时", "Quarter of day": "四分之一天", "Half of day": "半天", "Year": "年", "QuarterYear": "季度", "Select grouping field": "选择分组字段", "Refresh data blocks": "刷新数据区块", "Select data blocks to refresh": "选择要刷新的数据区块", "Media": "多媒体", "Markdown": "<PERSON><PERSON>", "Wysiwyg": "富文本", "Chart blocks": "图表区块", "Column chart": "柱状图", "Bar chart": "条形图", "Line chart": "折线图", "Pie chart": "饼图", "Area chart": "面积图", "Other chart": "其他图表", "Other blocks": "其他区块", "In configuration": "配置中", "Chart title": "图表标题", "Chart type": "图表类型", "Chart config": "图表配置", "Templates": "模板", "Template": "模板", "Select template": "选择模板", "Action logs": "操作日志", "Create template": "创建模板", "Edit markdown": "编辑 Markdown", "Add block": "创建区块", "Add new": "添加", "Add record": "添加数据", "Custom field display name": "自定义字段名称", "Display fields": "显示字段", "Edit record": "编辑数据", "Delete menu item": "删除菜单项", "Add page": "添加页面", "Add group": "添加分组", "Add link": "添加链接", "Insert above": "在上面插入", "Insert below": "在下面插入", "Save": "保存", "Delete block": "删除区块", "Are you sure you want to delete it?": "你确定要删除吗？", "This is a demo text, **supports Markdown syntax**.": "这是一段演示文本，**支持 Markdown 语法**。", "Filter": "筛选", "Connect data blocks": "连接数据区块", "Action type": "操作类型", "Actions": "操作", "Update": "更新", "Update record": "更新数据", "Unnamed": "未命名", "View": "查看", "View record": "查看数据", "Refresh": "刷新", "Data changes": "数据变更", "Field name": "字段标识", "Before change": "变更前", "After change": "变更后", "Delete record": "删除数据", "Delete collection": "删除数据表", "Create collection": "创建数据表", "Collection display name": "数据表名称", "Collection name": "数据表标识", "Inherits": "继承", "Primary key, unique identifier, self growth": "主键、唯一标识、自增长", "Store the creation user of each record": "记录创建人", "Store the last update user of each record": "记录最后更新人", "Store the creation time of each record": "记录创建时间", "Store the last update time of each record": "记录最后更新时间", "More options": "更多选项", "Records can be sorted": "可以对行记录进行排序", "Calendar collection": "日历数据表", "General collection": "普通数据表", "SQL collection": "SQL数据表", "Connect to database view": "连接数据库视图", "Sync from database": "从数据库同步", "Source collections": "来源数据表", "Field source": "来源字段", "Preview": "预览", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.": "随机生成，可修改。支持英文、数字和下划线，必须以英文字母开头。", "Edit": "编辑", "Edit collection": "编辑数据表", "Configure field": "配置字段", "Configure fields": "配置字段", "Configure columns": "配置字段", "Edit field": "编辑字段", "Override": "重写", "Override field": "重写字段", "Configure fields of {{title}}": "「{{title}}」的字段配置", "Association fields filter": "关系筛选", "PK & FK fields": "主外键字段", "Association fields": "关系字段", "Choices fields": "选项字段", "System fields": "系统字段", "General fields": "普通字段", "Inherited fields": "继承字段", "Parent collection fields": "父表字段", "Basic": "基本类型", "Single line text": "单行文本", "Automatically remove heading and tailing spaces": "自动去除首尾空白字符", "Long text": "多行文本", "Phone": "手机号码", "Email": "电子邮箱", "Username": "用户名", "Null": "空值", "Boolean": "逻辑值", "Number": "数字", "Integer": "整数", "Percent": "百分比", "String": "字符串", "Password": "密码", "Advanced type": "高级类型", "Syntax references": "语法参考", "Math.js comes with a large set of built-in functions and constants, and offers an integrated solution to work with different data types.": "Math.js 包含大量内置函数和常量，并提供了集成的解决方案来处理不同的数据类型。", "Formula.js supports most Microsoft Excel formula functions.": "Formula.js 支持大部分 Mircrosoft Excel 公式。", "String template": "字符串模板", "Simple string replacement, can be used to interpolate variables in a string.": "简单的字符串替换，可以用于在字符串中插入变量。", "https://docs.nocobase.com/handbook/calculation-engines/formula": "https://docs-cn.nocobase.com/handbook/calculation-engines/formula", "https://docs.nocobase.com/handbook/calculation-engines/mathjs": "https://docs-cn.nocobase.com/handbook/calculation-engines/mathjs", "Choices": "选择类型", "Checkbox": "勾选", "Display <icon></icon> when unchecked": "未勾选时显示 <icon></icon>", "Single select": "下拉菜单（单选）", "Multiple select": "下拉菜单（多选）", "Radio group": "单选框", "Checkbox group": "复选框", "China region": "中国行政区", "Date & Time": "日期 & 时间", "Datetime": "日期时间", "Relation": "关系类型", "Link to": "关联", "Link to description": "用于快速创建表关系，可兼容大多数普通场景。适合非开发人员使用。作为字段存在时，它是一个下拉选择用于选择目标数据表的数据。创建后，将同时在目标数据表中生成当前数据表的关联字段。", "Sub-table": "子表格", "System info": "系统信息", "Created at": "创建日期", "Last updated at": "最后修改日期", "Created by": "创建人", "Last updated by": "最后修改人", "Add field": "添加字段", "Field display name": "字段名称", "Field type": "字段类型", "Field interface": "字段类型", "Date format": "日期格式", "Year/Month/Day": "年/月/日", "Year-Month-Day": "年-月-日", "Day/Month/Year": "日/月/年", "Show time": "显示时间", "Time format": "时间格式", "12 hour": "12 小时制", "24 hour": "24 小时制", "Relationship type": "关系类型", "Inverse relationship type": "反向关系类型", "Source collection": "源数据表", "Source key": "源数据表标识字段", "Target collection": "目标数据表", "Through collection": "中间数据表", "Target key": "目标数据表标识字段", "Foreign key": "外键", "One to one": "一对一", "One to many": "一对多", "Many to one": "多对一", "Many to many": "多对多", "Foreign key 1": "外键1", "Foreign key 2": "外键2", "One to one description": "用于创建一对一关系，比如一个用户会有一套个人资料。", "One to many description": "用于创建一对多关系，比如一个国家会有多个城市。作为字段存在时，它是一个子表格用于显示目标数据表的数据。创建后，会在目标数据表里自动生成一个多对一字段。", "Many to one description": "用于创建多对一关系，比如一个城市只能属于一个国家，一个国家可以有多个城市。作为字段存在时，它是一个下拉选择用于选择目标数据表的数据。创建后，会在目标数据表里自动生成一个多对一字段。", "Many to many description": "用于创建多对多关系，比如一个学生会有多个老师，一个老师也会有多个学生。作为字段存在时，它是一个下拉选择用于选择目标数据表的数据。", "Generated automatically if left blank": "留空时，自动生成中间表", "Display association fields": "显示关联表的字段", "Display field title": "显示字段标题", "Field component": "字段组件", "Allow multiple": "允许添加/关联多条", "Allow dissociate": "允许移除已关联记录", "Quick upload": "快速上传", "Select file": "选择文件", "Subtable": "子表格", "Sub-form": "子表单", "Sub-form(Popover)": "子表单(弹窗)", "Sub-details": "子详情", "Record picker": "数据选择器", "Toggles the subfield mode": "切换子字段模式", "Selector mode": "选择器模式", "Subtable mode": "子表格模式", "Subform mode": "子表单模式", "Field mode": "字段组件", "Allow add new data": "允许添加数据", "Edit block title & description": "编辑区块标题和描述", "Block title": "区块标题", "Pattern": "模式", "Operator": "运算符", "Editable": "可编辑", "Readonly": "只读（禁止编辑）", "Easy-reading": "只读（阅读模式）", "Add filter": "添加筛选条件", "Add filter group": "添加筛选分组", "Comparision": "值比较", "is": "等于", "is not": "不等于", "contains": "包含", "does not contain": "不包含", "starts with": "开头是", "not starts with": "开头不是", "ends with": "结尾是", "not ends with": "结尾不是", "is empty": "为空", "is not empty": "不为空", "Edit chart": "编辑图表", "Add Markdown": "添加 Markdown", "Filterable fields": "可筛选字段", "Edit button": "编辑按钮", "Hide": "隐藏", "Enable actions": "启用操作", "Import": "导入", "Export": "导出记录", "Customize": "自定义", "Custom": "自定义", "Function": "Function", "Popup form": "Popup form", "Flexible popup": "Flexible popup", "Configure actions": "配置操作", "Display order number": "显示序号", "Enable drag and drop sorting": "启用拖拽排序", "Triggered when the row is clicked": "点击表格行时触发", "Add tab": "添加标签页", "Disable tabs": "禁用标签页", "Details": "详情", "Edit form": "编辑表单", "Create form": "创建表单", "Form (Edit)": "表单（编辑）", "Form (Add new)": "表单（添加）", "Edit tab": "编辑标签页", "Relationship blocks": "关系数据区块", "Select record": "选择数据", "Display name": "显示名称", "Select icon": "选择图标", "Custom column name": "自定义列名称", "Edit description": "编辑描述", "Required": "必填", "Unique": "不允许重复", "Primary": "主键", "Auto increment": "自动增长", "Label field": "标签字段", "Default is the ID field": "默认为 ID 字段", "Set default sorting rules": "设置排序规则", "Set validation rules": "设置验证规则", "Max length": "最大长度", "Min length": "最小长度", "Maximum": "最大值", "Minimum": "最小值", "Max length must greater than min length": "最大长度必须大于最小长度", "Min length must less than max length": "最小长度必须小于最大长度", "Maximum must greater than minimum": "最大值必须大于最小值", "Minimum must less than maximum": "最小值必须小于最大值", "Validation rule": "验证规则", "Add validation rule": "新增验证规则", "Format": "格式", "Regular expression": "正则表达式", "Error message": "错误消息", "Length": "长度", "The field value cannot be greater than ": "数值不能大于", "The field value cannot be less than ": "数值不能小于", "The field value is not an integer number": "数字不是整数", "Set default value": "设置默认值", "Default value": "默认值", "is before": "早于", "is after": "晚于", "is on or after": "不早于", "is on or before": "不晚于", "is between": "介于", "Upload": "上传", "Select level": "选择层级", "Province": "省", "City": "市", "Area": "区/县", "Street": "乡镇/街道", "Village": "村/居委会", "Must select to the last level": "必须选到最后一级", "Move {{title}} to": "将 {{title}} 移动到", "Target position": "目标位置", "After": "之后", "Before": "之前", "Add {{type}} before \"{{title}}\"": "在 \"{{title}}\" 前插入{{type}}", "Add {{type}} after \"{{title}}\"": "在 \"{{title}}\" 前插入{{type}}", "Add {{type}} in \"{{title}}\"": "在 \"{{title}}\" 里插入{{type}}", "Original name": "原名称", "Custom name": "自定义名称", "Custom Title": "自定义标题", "Options": "选项", "Option value": "选项值", "Option label": "选项标签", "Color": "颜色", "Background Color": "背景颜色", "Text Align": "文本对齐", "Add option": "添加选项", "Related collection": "关系表", "Allow linking to multiple records": "允许关联多条记录", "Daily": "每天", "Weekly": "每周", "Monthly": "每月", "Yearly": "每年", "Repeats": "重复", "Configure calendar": "配置日历", "Title field": "标题字段", "Custom title": "自定义标题", "Show lunar": "展示农历", "Start date field": "开始日期字段", "End date field": "结束日期字段", "Navigate": "分页", "Title": "标题", "Description": "描述", "Select view": "切换视图", "Reset": "重置", "Importable fields": "可导入字段", "Exportable fields": "可导出字段", "Saved successfully": "保存成功", "Nickname": "昵称", "Sign in": "登录", "Sign in via account": "账号密码登录", "Sign in via phone": "手机号登录", "Create an account": "注册账号", "Sign up": "注册", "Confirm password": "确认密码", "Log in with an existing account": "使用已有账号登录", "Signed up successfully. It will jump to the login page.": "注册成功，将跳转登录页。", "Password mismatch": "重复密码不匹配", "Users": "用户", "Verification code": "验证码", "Send code": "发送验证码", "Retry after {{count}} seconds": "{{count}} 秒后重试", "Must be 1-50 characters in length (excluding @.<>\"'/)": "长度为1到50个字符（不能包含@.<>\"'/）", "Roles": "角色", "Add role": "添加角色", "Role name": "角色名称", "Configure": "配置", "Configure permissions": "配置权限", "Edit role": "编辑角色", "Action permissions": "数据表操作权限", "Menu permissions": "菜单访问权限", "Menu item name": "菜单名称", "Allow access": "允许访问", "Action name": "操作名称", "Allow action": "允许操作", "Action scope": "可操作数据范围", "Operate on new data": "对新增数据操作", "Operate on existing data": "对已有数据操作", "Yes": "是", "No": "否", "Red": "薄暮", "Magenta": "法式洋红", "Volcano": "火山", "Orange": "日暮", "Gold": "金盏花", "Lime": "青柠", "Green": "极光绿", "Cyan": "<PERSON>青", "Blue": "拂晓蓝", "Geek blue": "极客蓝", "Purple": "酱紫", "Default": "默认", "Add card": "添加卡片", "edit title": "修改标题", "Turn pages": "翻页", "Others": "其他", "Other records": "其他记录", "Save as reference template": "保存为引用模板", "Save as inherited template": "保存为继承模板", "Save as block template": "保存为区块模板", "Block templates": "区块模板", "Block template": "区块模板", "Convert reference to duplicate": "模板引用转为复制", "Template name": "模板名称", "Block type": "区块类型", "No blocks to connect": "没有可连接的区块", "Action column": "操作列", "Records per page": "每页显示数量", "(Fields only)": "（仅字段）", "Button title": "按钮标题", "Button icon": "按钮图标", "Submitted successfully": "提交成功", "Operation succeeded": "操作成功", "Operation failed": "操作失败", "Open mode": "打开方式", "Popup size": "弹窗尺寸", "Small": "较窄", "Middle": "中等", "Large": "较宽", "Size": "大小", "Oversized": "超大", "Auto": "自动", "Object Fit": "适应", "Cover": "覆盖", "Fill": "填充", "Contain": "包含", "Scale Down": "缩放", "Menu item title": "菜单项名称", "Menu item icon": "菜单项图标", "Target": "目标", "Position": "位置", "Insert before": "在前面插入", "Insert after": "在后面插入", "UI Editor": "界面配置", "ASC": "升序", "DESC": "降序", "Add sort field": "添加排序字段", "ID": "ID", "Identifier for program usage. Support letters, numbers and underscores, must start with an letter.": "用于程序使用的标识符，支持字母、数字和下划线，必须以字母开头。", "Drawer": "抽屉", "Dialog": "对话框", "Delete action": "删除操作", "Custom column title": "自定义列标题", "Column title": "列标题", "Original title: ": "原始标题: ", "Delete table column": "删除列", "Skip required validation": "跳过必填校验", "Form values": "表单值", "Fields values": "字段值", "The field has been deleted": "字段已删除", "When submitting the following fields, the saved values are": "提交以下字段时，保存的值为", "After successful submission": "提交成功后", "Then": "然后", "Stay on current page": "停留在当前页面", "Redirect to": "跳转到", "Save action": "保存操作", "Exists": "存在", "Add condition": "添加条件", "Add condition group": "添加条件分组", "exists": "存在", "not exists": "不存在", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "角色标识", "Precision": "精确度", "Rich Text": "富文本", "Junction collection": "中间表", "Leave it blank, unless you need a custom intermediate table": "默认留空，除非你需要一个自定义的中间表", "Fields": "字段", "Edit field title": "编辑字段标题", "Field title": "字段标题", "Original field title: ": "原始字段标题：", "Edit tooltip": "编辑提示信息", "Delete field": "删除字段", "Select collection": "选择数据表", "Blank block": "空区块", "Duplicate template": "复制模板", "Reference template": "引用模板", "Inherited template": "继承模板", "Create calendar block": "创建日历区块", "Create kanban block": "创建看板区块", "Grouping field": "分组字段", "Single select and radio fields can be used as the grouping field": "数据表的单选字段可以作为分组字段", "Tab name": "标签名称", "Current record blocks": "当前数据区块", "Popup message": "弹窗提示消息", "Delete role": "删除角色", "Role display name": "角色名称", "Default role": "默认角色", "All collections use general action permissions by default; permission configured individually will override the default one.": "所有数据表都默认使用通用数据操作权限；同时，可以针对每个数据表单独配置权限。", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "允许配置系统，包括界面配置、数据表配置、权限配置、系统配置等全部配置项", "New menu items are allowed to be accessed by default.": "新增菜单项默认允许访问", "Global permissions": "全局配置", "General permissions": "通用配置", "Global action permissions": "全局操作权限", "General action permissions": "通用操作权限", "Plugin settings permissions": "插件配置权限", "Data source permissions": "数据源权限", "Allow to desgin pages": "允许界面配置", "Allow to manage plugins": "允许管理插件", "Allow to configure plugins": "允许管理配置中心", "Allows to configure interface": "允许配置界面", "Allows to install, activate, disable plugins": "允许安装、激活、禁用插件", "Allows to configure plugins": "允许配置插件", "Action display name": "操作名称", "Allow": "允许", "Data scope": "数据范围", "Action on new records": "对新增数据操作", "Action on existing records": "对已有数据操作", "All records": "所有数据", "Own records": "自己的数据", "Permission policy": "权限策略", "Individual": "单独配置", "General": "通用配置", "Accessible": "允许访问", "Configure permission": "配置权限", "Action permission": "操作权限", "Field permission": "字段权限", "Scope name": "数据范围名称", "Unsaved changes": "未保存修改", "Are you sure you don't want to save?": "你确定不保存修改吗？", "Dragging": "拖拽中", "Popup": "打开弹窗", "Trigger workflow": "触发工作流", "Request API": "请求API", "Assign field values": "字段赋值", "Constant value": "静态值", "Dynamic value": "动态值", "Current user": "当前用户", "Current role": "当前角色", "Current record": "当前记录", "Current collection": "当前数据表", "Other collections": "其他数据表", "Current popup record": "当前弹窗记录", "Parent popup record": "上级弹窗记录", "Associated records": "关联记录", "Parent record": "上级记录", "Current time": "当前时间", "Now": "现在", "Message popup close method": "消息弹窗关闭方式", "Automatic close": "自动关闭", "Manually close": "手动关闭", "After successful update": "更新成功后", "Save record": "保存数据", "Updated successfully": "更新成功", "After successful save": "保存成功后", "After clicking the custom button, the following field values will be assigned according to the following form.": "点击当前自定义按钮时，以下字段值将按照以下表单赋值。", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "点击当前自定义按钮时，当前数据以下字段将按照以下表单保存。", "Button background color": "按钮颜色", "Highlight": "高亮", "Danger red": "红色", "Custom request": "自定义请求", "Request settings": "请求设置", "Request URL": "请求地址", "Request method": "请求方法", "Request query parameters": "请求查询参数(JSON格式)", "Request headers": "请求头参数(JSON格式)", "Request body": "请求体(JSON格式)", "Request success": "请求成功", "Invalid JSON format": "非法JSON格式", "After successful request": "请求成功之后", "Add exportable field": "添加可导出字段", "Audit logs": "操作记录", "Record ID": "数据 ID", "User": "用户", "Field": "字段", "Select": "选择", "Select field": "选择字段", "Field value changes": "变更记录", "One to one (has one)": "一对一（has one）", "One to one (belongs to)": "一对一（belongs to）", "Use the same time zone (GMT) for all users": "所有用户使用同一时区 (格林尼治标准时间)", "Province/city/area name": "省市区名称", "Enabled languages": "启用的语言", "View all plugins": "查看所有插件", "Print": "打印", "Done": "完成", "Sign up successfully, and automatically jump to the sign in page": "注册成功，即将跳转到登录页面", "ACL": "访问控制", "Access control": "访问控制", "Collection manager": "数据表管理", "Plugin manager": "插件管理器", "Local": "本地", "Built-in": "内置", "Marketplace": "插件市场", "Add plugin": "新增插件", "Upgrade": "可供更新", "Plugin dependencies check failed": "插件依赖检查失败", "Remove": "移除", "Docs": "文档", "More details": "更多详情", "Upload new version": "上传新版", "Official plugin": "官方插件", "Version": "版本", "Npm package": "Npm 包", "Upload plugin": "上传插件", "Npm package name": "Npm 包名", "Add type": "新增方式", "Plugin source": "插件来源", "Changelog": "更新日志", "Dependencies check": "依赖检查", "Update plugin": "更新插件", "Installing": "安装中", "The deletion was successful.": "删除成功", "Plugin Zip File": "插件压缩包", "Compressed file url": "压缩包地址", "Last updated": "最后更新", "PackageName": "包名", "DisplayName": "显示名称", "Readme": "说明文档", "Dependencies compatibility check": "依赖兼容性检查", "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.": "插件兼容性检查失败，你应该修改依赖版本以满足版本要求。", "Version range": "版本范围", "Plugin's version": "插件的版本", "Result": "结果", "No CHANGELOG.md file": "没有 CHANGELOG.md 日志", "No README.md file": "没有 README.md 文件", "Homepage": "主页", "Drag and drop the file here or click to upload, file size should not exceed 30M": "将文件拖放到此处或单击上传，文件大小不应超过 30M", "Dependencies check failed, can't enable.": "依赖检查失败，无法启用。", "Plugin starting...": "插件启动中...", "Plugin stopping...": "插件停止中...", "Are you sure to delete this plugin?": "确定要删除此插件吗？", "Are you sure to disable this plugin?": "确定要禁用此插件吗？", "re-download file": "重新下载文件", "Not enabled": "未启用", "Search plugin": "搜索插件", "Author": "作者", "Plugin loading failed. Please check the server logs.": "插件加载失败，请检查服务器日志。", "Coming soon...": "敬请期待...", "All plugin settings": "所有插件配置", "Bookmark": "书签", "Manage all settings": "管理所有配置", "Create inverse field in the target collection": "在目标数据表里创建反向关系字段", "Inverse field name": "反向关系字段标识", "Inverse field display name": "反向关系字段名称", "Bulk update": "批量更新", "After successful bulk update": "批量成功更新后", "Bulk edit": "批量编辑", "Data will be updated": "更新的数据", "Selected": "选中", "All": "所有", "Update selected data?": "更新选中的数据吗？", "Update all data?": "更新全部数据吗？", "Remains the same": "不更新", "Changed to": "修改为", "Clear": "清空", "Add attach": "增加关联", "Please select the records to be updated": "请选择要更新的记录", "Selector": "选择器", "Inner": "里面", "Search and select collection": "搜索并选择数据表", "Please fill in the iframe URL": "请填写嵌入的地址", "Fix block": "固定区块", "Plugin name": "插件", "Plugin tab name": "插件标签页", "Enable page header": "启用页眉", "Display page title": "显示页面标题", "Edit page title": "编辑页面标题", "Enable page tabs": "启用页面选项卡", "Enable link": "启用链接", "Column width": "列宽", "Sortable": "可排序的", "Constant": "常量", "Select a variable": "选择变量", "Insert": "插入", "Insert if not exists": "不存在时插入", "Insert if not exists, or update": "不存在时插入，否则更新", "System variables": "系统变量", "Date variables": "日期变量", "Double click to choose entire object": "双击选择整个对象", "True": "真", "False": "假", "Prettify": "格式化", "Theme": "主题", "Default theme": "默认主题", "Compact theme": "紧凑主题", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "这可能是 NocoBase 内部的问题，你可以在<1>这里</1>将该错误反馈给我们，我们会尽快修复", "Render Failed": "渲染失败", "Feedback": "反馈问题", "Try again": "重试一下", "Download logs": "下载日志", "Download": "下载", "File type is not supported for previewing, please download it to preview.": "不支持预览该文件类型，请下载后查看。", "Click or drag file to this area to upload": "点击或拖拽文件到此区域上传", "Support for a single or bulk upload.": "支持单个或批量上传", "File size should not exceed {{size}}.": "文件大小不能超过 {{size}}", "File size exceeds the limit": "文件大小超过限制", "File type is not allowed": "文件类型不允许", "Uploading": "上传中", "Some files are not uploaded correctly, please check.": "部分文件未上传成功，请检查。", "Default title for each record": "用作数据的默认标题", "If collection inherits, choose inherited collections as templates": "当前表有继承关系时，可选择继承链路上的表作为模板来源", "Select an existing piece of data as the initialization data for the form": "选择一条已有的数据作为表单的初始化数据", "Only the selected fields will be used as the initialization data for the form": "仅选择的字段才会作为表单的初始化数据", "Template Data": "模板数据", "Data fields": "数据字段", "Add template": "添加模板", "Enable form data template": "启用表单数据模板", "Form data templates": "表单数据模板", "Data template": "数据模板", "No configuration available.": "无可配置项。", "Reload application": "重载应用", "The application is reloading, please do not close the page.": "应用正在重新加载，请勿关闭页面。", "Application reloading": "应用重新加载中", "Restart application": "重启应用", "Allows to clear cache, reboot application": "允许清除缓存，重启应用", "The will interrupt service, it may take a few seconds to restart. Are you sure to continue?": "重启将会中断当前服务，这个过程可能需要一点时间，确定要继续吗？", "Restart": "重启", "Clear cache": "清除缓存", "Are you sure you want to clear cache ?": "你确定你想清除缓存吗", "Duplicate": "复制", "Duplicating": "复制中", "Duplicate mode": "复制方式", "Quick duplicate": "快速复制", "Duplicate and continue": "复制并继续", "Please configure the duplicate fields": "请配置要复制的字段", "Add": "创建", "Add new mode": "添加方式", "Quick add": "快捷添加", "Modal add": "弹窗添加", "Save mode": "保存方式", "First or create": "不存在时则新增,存在时不处理", "Update or create": "不存在时新增,存在时更新", "Find by the following fields": "通过以下字段查找", "Create": "仅新增", "Current form": "当前表单", "Current object": "当前对象", "Quick create": "快速创建", "Dropdown": "下拉菜单", "Pop-up": "弹窗", "File manager": "文件管理器", "Direct duplicate": "直接复制", "Copy into the form and continue to fill in": "复制到表单并继续填写", "Linkage with form fields": "从表单字段联动", "App error": "应用错误", "Failed to load plugin": "插件加载失败", "Allow add new, update and delete actions": "允许增删改操作", "Date display format": "日期显示格式", "Date range limit": "日期限定范围", "MinDate": "最小日期", "MaxDate": "最大日期", "Please select time or variable": "请选择时间或变量", "Filter out a single piece or a group of records as a template": "筛选出一条或一组数据，作为模板", "The title field is used to identify the template record": "用于识别模板数据", "Template fields": "模板字段", "The selected fields will automatically populate the form": "用于自动填充到表单", "Table selected records": "表格中选中的记录", "Tag": "标签", "Tag color field": "标签颜色字段", "Sync successfully": "同步成功", "Sync from form fields": "同步表单字段", "Select all": "全选", "UnSelect all": "取消全选", "Determine whether a record exists by the following fields": "通过以下字段判断记录是否存在", "Cascade Select": "级联选择", "Execute": "执行", "Please use a valid SELECT or WITH AS statement": "请使用有效的 SELECT 或 WITH AS 语句", "Please confirm the SQL statement first": "请先确认 SQL 语句", "Automatically drop objects that depend on the collection (such as views), and in turn all objects that depend on those objects": "自动删除依赖于该表的对象,以及依赖这些对象的对象", "Secondary confirmation": "二次确认", "Perform the {{title}}": "执行{{title}}", "Are you sure you want to perform the {{title}} action?": "你确定执行{{title}}操作吗？", "Sign in with another account": "登录其他账号", "Return to the main application": "返回主应用", "Permission denied": "没有权限", "Allow add new": "允许新增", "loading": "加载中", "name is required": "名称不能为空", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "{{type}} \"{{name}}\" 可能已被删除。请删除当前{{blockType}}.", "data source": "数据源", "Data source": "数据源", "DataSource": "数据源", "Data model": "数据模型", "Security": "安全性", "Action": "操作", "System": "系统管理", "Other": "其他", "Allow selection of existing records": "允许选择已有数据", "Data Model": "数据模型", "Blocks": "区块", "Users & permissions": "用户和权限", "System management": "系统管理", "Preset fields": "预置字段", "System & security": "系统和安全", "Workflow": "工作流", "Third party services": "第三方服务", "Data model tools": "数据模型工具", "Data sources": "数据源", "Collections": "数据表", "Collection fields": "数据表字段", "Authentication": "用户认证", "Notification": "通知", "Logging and monitoring": "日志与监控", "Home page": "主页", "Handbook": "用户手册", "License": "许可证", "Generic properties": "通用属性", "Specific properties": "特有属性", "Used for drag and drop sorting scenarios, supporting grouping sorting": "用于拖拽排序场景，支持分组排序", "Grouped sorting": "分组排序", "When a field is selected for grouping, it will be grouped first before sorting.": "当选了某个字段作为分组时，将先分组再排序", "Main": "主数据源", "Index": "索引", "Field values must be unique.": "字段值必须具备唯一性", "Departments": "部门", "Main department": "主属部门", "Department name": "部门名称", "Superior department": "上级部门", "Owners": "负责人", "Plugin settings": "插件设置", "Menu": "菜单", "Drag and drop sorting field": "拖拽排序字段", "Alphabet": "字符", "Accuracy": "精确度", "Millisecond": "毫秒", "Second": "秒", "Unix Timestamp": "Unix 时间戳", "Field value do not meet the requirements": "字符不符合要求", "Field value size is": "字符长度要求", "Style": "样式", "Unit conversion": "单位换算", "Separator": "分隔符", "Prefix": "前缀", "Suffix": "后缀", "Record unique key": "记录唯一标识符", "Filter target key": "筛选目标键", "If a collection lacks a primary key, you must configure a unique record key to locate row records within a block, failure to configure this will prevent the creation of data blocks for the collection.": "当数据表没有主键时，你需要配置记录唯一标识符，用于在区块中定位行记录，不配置将无法创建该表的数据区块。", "Filter data based on the specific field, with the requirement that the field value must be unique.": "根据特定的字段筛选数据，字段值必须具备唯一性。", "Multiply by": "乘以", "Divide by": "除以", "Scientifix notation": "科学计数法", "Normal": "常规", "Automatically generate default values": "随机生成默认值", "Refresh data on close": "关闭后刷新数据", "Refresh data on action": "执行后刷新数据", "This variable has been deprecated and can be replaced with \"Current form\"": "该变量已被弃用，可以使用“当前表单”替代", "Unknown field type": "未知字段类型", "The following field types are not compatible and do not support output and display": "以下字段类型未适配，不支持输出和显示", "Not fixed": "不固定", "Left fixed": "左固定", "Right fixed": "右固定", "Fixed": "固定列", "Set block height": "设置区块高度", "Specify height": "指定高度", "Full height": "全高", "Please configure the URL": "请配置URL", "URL": "URL", "Search parameters": "URL 查询参数", "Do not concatenate search params in the URL": "查询参数不要在 URL 里拼接", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "该变量的值是根据页面 URL 的 query string 得来的，只有当页面存在 query string 的时候，该变量才能正常使用。", "Edit link": "编辑链接", "Add parameter": "添加参数", "URL search params": "URL 查询参数", "Expand All": "展开全部", "Search": "搜索", "Clear default value": "清除默认值", "Open in new window": "新窗口打开", "is none of": "不等于任意一个", "is any of": "等于任意一个", "Use simple pagination mode": "使用简单分页模式", "Sorry, the page you visited does not exist.": "抱歉，你访问的页面不存在。", "Set Template Engine": "设置模板引擎", "Template engine": "模板引擎", "Skip getting the total number of table records during paging to speed up loading. It is recommended to enable this option for data tables with a large amount of data": "在分页时跳过获取表记录总数，以加快加载速度，建议对有大量数据的数据表开启此选项", "The current user only has the UI configuration permission, but don't have view permission for collection \"{{name}}\"": "当前用户只有 UI 配置权限，但没有数据表 \"{{name}}\" 查看权限。", "Plugin dependency version mismatch": "插件依赖版本不一致", "The current dependency version of the plugin does not match the version of the application and may not work properly. Are you sure you want to continue enabling the plugin?": "当前插件的依赖版本与应用的版本不一致，可能无法正常工作。您确定要继续激活插件吗？", "Allow multiple selection": "允许多选", "Parent object": "上级对象", "Default value to current time": "设置字段默认值为当前时间", "Automatically update timestamp on update": "当记录更新时自动设置字段值为当前时间", "Default value to current server time": "设置字段默认值为当前服务端时间", "Automatically update timestamp to the current server time on update": "当记录更新时自动设置字段值为当前服务端时间", "Datetime (with time zone)": "日期时间（含时区）", "Datetime (without time zone)": "日期时间（不含时区）", "DateOnly": "仅日期", "Enable secondary confirmation": "启用二次确认", "Content": "内容", "Perform the Update record": "执行更新数据", "Are you sure you want to perform the Update record action?": "你确定执行更新数据操作吗？", "Perform the Custom request": "执行自定义请求", "Are you sure you want to perform the Custom request action": "你确定执行自定义请求操作吗？", "Perform the Refresh": "执行刷新", "Are you sure you want to perform the Refresh action?": "你确定执行刷新操作吗？", "Perform the Submit": "执行提交", "Are you sure you want to perform the Submit action?": "你确定执行提交操作吗？", "Perform the Trigger workflow": "执行触发工作流", "Are you sure you want to perform the Trigger workflow action?": "你确定执行触发工作流吗？", "Ellipsis overflow content": "省略超出长度的内容", "Picker": "选择器", "Quarter": "季度", "Switching the picker, the value and default value will be cleared": "切换选择器时，字段的值和默认值将会被清空", "Stay on the current popup or page": "停留在当前弹窗或页面", "Return to the previous popup or page": "返回上一层弹窗或页面", "Action after successful submission": "提交成功后动作", "Allow disassociation": "允许解除已有数据关联", "Layout": "布局", "Vertical": "垂直", "Horizontal": "水平", "Edit group title": "编辑分组标题", "Title position": "标题位置", "Dashed": "虚线", "Left": "左", "Center": "居中", "Right": "右", "Divider line color": "分割线颜色", "Label align": "字段标题对齐方式", "Label width": "字段标题宽度", "When the Label exceeds the width": "字段标题超出宽度时", "Line break": "换行", "Ellipsis": "省略", "Set block layout": "设置区块布局", "Add & Update": "添加 & 更新", "Table size": "表格大小", "Hide column": "隐藏列", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "在配置模式下，整个列会变为透明色。在非配置模式下，整个列将被隐藏。即使整个列被隐藏了，其配置的默认值和其他设置仍然有效。", "Plugin": "插件", "Bulk enable": "批量激活", "Search plugin...": "搜索插件...", "Package name": "包名", "Associate": "关联", "Please add or select record": "请添加或选择数据", "No data": "暂无数据", "Fields can only be used correctly if they are defined with an interface.": "只有字段设置了interface字段才能正常使用", "Unauthenticated. Please sign in to continue.": "未认证。请登录以继续。", "User not found. Please sign in again to continue.": "无法找到用户信息，请重新登录以继续。", "Your session has expired. Please sign in again.": "您的会话已过期，请重新登录。", "User password changed, please signin again.": "用户密码已更改，请重新登录。", "Show file name": "显示文件名", "Outlined": "线框风格", "Filled": "实底风格", "Two tone": "双色风格", "Desktop routes": "桌面端路由", "Route permissions": "路由权限", "New routes are allowed to be accessed by default": "新路由默认允许访问", "Route name": "路由名称", "Mobile routes": "移动端路由", "Show in menu": "在菜单中显示", "Hide in menu": "在菜单中隐藏", "Path": "路径", "Type": "类型", "Access": "访问", "Routes": "路由", "Add child route": "添加子路由", "Delete routes": "删除路由", "Delete route": "删除路由", "Are you sure you want to hide these routes in menu?": "你确定要在菜单中隐藏这些路由吗？", "Are you sure you want to show these routes in menu?": "你确定要在菜单中显示这些路由吗？", "Are you sure you want to hide this menu?": "你确定要隐藏这个菜单吗？", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "隐藏后，这个菜单将不再出现在菜单栏中。要再次显示它，你需要到路由管理页面进行设置。", "If selected, the page will display Tab pages.": "如果选中，该页面将显示标签页。", "If selected, the route will be displayed in the menu.": "如果选中，该路由将显示在菜单中。", "Are you sure you want to hide this tab?": "你确定要隐藏该标签页吗？", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "隐藏后，该标签将不再显示在标签栏中。要想再次显示它，你需要到路由管理页面进行设置。", "Deprecated": "已弃用", "Full permissions": "全部权限", "Enable index column": "启用序号列", "Date scope": "日期范围", "Icon only": "仅显示图标", "Valid range: 100-900": "有效范围：100-900", "Valid range: 10-40": "有效范围：10-40", "Font Size（px）": "字体大小（像素）", "Font Weight": "字体粗细", "Font Style": "字体样式", "Italic": "斜体", "Response record": "响应结果记录", "Colon": "冒号", "No pages yet, please configure first": "暂无页面，请先配置", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "点击右上角的“界面配置”图标，进入界面配置模式", "Specifies a Permissions Policy for the <iframe>. The policy defines what features are available to the <iframe> (for example, access to the microphone, camera, battery, web-share, etc.) based on the origin of the request.": "用于为 <iframe> 指定其权限策略。该策略根据请求的来源规定 <iframe> 可以使用哪些特性（例如，访问麦克风、摄像头、电池、web 共享等）。", "Controls whether the current document is allowed to autoplay media requested through the HTMLMediaElement interface. When this policy is disabled and there were no user gestures, the Promise returned by HTMLMediaElement.play() will reject with a NotAllowedError DOMException. The autoplay attribute on <audio> and <video> elements will be ignored.": "控制是否允许当前文档自动播放媒体。这种控制是通过接口 HTMLMediaElement 来实现。当这种规则被禁用，而且没有用户操作的时候，HTMLMediaElement.play() 返回的 Promise 会拒绝并抛出一个 DOMException 异常。<audio> 和 <video> 上的 autoplay 属性会被忽略。", "Controls whether the current document is allowed to use video input devices. When this policy is disabled, the Promise returned by getUserMedia() will reject with a NotAllowedError DOMException.": "控制是否允许当前文档使用视频输入设备。当这种规则被禁用时，MediaDevices.getUserMedia() 返回的 Promise 会拒绝并抛出 NotAllowedError DOMException 异常。", "Controls whether the current document is allowed to set document.domain. When this policy is disabled, attempting to set document.domain will fail and cause a SecurityError DOMException to be thrown.": "控制是否允许当前文档设置 document.domain。当这种规则被禁用时，尝试设置 document.domain 会失败并抛出 SecurityError DOMException 异常。", "Controls whether the current document is allowed to use the Encrypted Media Extensions API (EME). When this policy is disabled, the Promise returned by Navigator.requestMediaKeySystemAccess() will reject with a SecurityError DOMException.": "控制是否允许当前文档使用 Encrypted Media Extension API（EME）。当这种规则被禁用时，Navigator.requestMediaKeySystemAccess() 返回的 Promise 会拒绝并抛出 DOMException 异常。", "Controls whether the current document is allowed to use Element.requestFullscreen(). When this policy is disabled, the returned Promise rejects with a TypeError.": "控制是否允许当前文档使用 Element.requestFullScreen()。当这种规则被禁用时，返回的 Promise 会拒绝并抛出 TypeError。", "Controls whether the current document is allowed to use the Geolocation Interface. When this policy is disabled, calls to getCurrentPosition() and watchPosition() will cause those functions callbacks to be invoked with a GeolocationPositionError code of PERMISSION_DENIED.": "控制是否允许当前文档使用 Geolocation 接口。当这种规则被禁用时，调用 getCurrentPosition() 和 watchPosition() 会返回包含 PERMISSION_DENIED 的 PositionError。", "Controls whether the current document is allowed to use audio input devices. When this policy is disabled, the Promise returned by MediaDevices.getUserMedia() will reject with a NotAllowedError DOMException.": "控制是否允许当前文档使用音频输入设备。当这种规则被禁用时，MediaDevices.getUserMedia() 返回的 Promise 会拒绝并抛出错误 NotAllowedError。", "Controls whether the current document is allowed to use the Web MIDI API. When this policy is disabled, the Promise returned by Navigator.requestMIDIAccess() will reject with a SecurityError DOMException.": "控制是否允许当前文档使用 Web MIDI API。当这种规则被禁用时，Navigator.requestMIDIAccess() 返回的 Promise 会拒绝并抛出错误 DOMException。", "Controls whether the current document is allowed to use the Payment Request API. When this policy is enabled, the PaymentRequest() constructor will throw a SecurityError DOMException.": "控制是否允许当前文档使用 Payment Request API。当这种规则被启用时，构造函数 PaymentRequest() 会抛出错误 SecurityError。", "After successful submission, the selected data blocks will be automatically refreshed.": "提交成功后，会自动刷新这里选中的数据区块。", "Block Linkage rules": "区块联动规则", "Field Linkage rules": "字段联动规则", "Reset link expiration": "重置链接有效期", "Exact day": "指定日期", "This Week": "本周", "Last Week": "上周", "Next Week": "下周", "This Month": "本月", "Last Month": "上个月", "Next Month": "下个月", "This Quarter": "本季度", "Last Quarter": "上季度", "Next Quarter": "下季度", "This Year": "今年", "Last Year": "去年", "Next Year": "明年", "Past": "过去", "Next": "未来", "Calendar week": "日历周", "Calendar Month": "日历月", "Calendar Year": "日历年", "Scan to input": "扫码录入", "Disable manual input": "禁止手动输入", "Enable Scan": "启用扫码录入", "Date variables(Deprecated)": "日期变量(废弃)"}