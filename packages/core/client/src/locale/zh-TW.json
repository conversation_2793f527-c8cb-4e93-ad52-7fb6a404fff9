{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "每頁顯示 <1><0>10</0><1>20</1><2>50</2><3>100</3></1> 項", "Page number": "頁碼", "Page size": "每頁列數", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "滿足群組內 <1><0>全部</0><1>任意</1></1> 條件", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "在 <1><0>對話方塊</0><1>側欄</1><2>彈出視窗</2></1> 內開啟", "{{count}} filter items": "{{count}} 個篩選項目", "{{count}} more items": "還有 {{count}} 項", "Total {{count}} items": "總共 {{count}} 項", "Today": "今天", "Yesterday": "昨天", "Tomorrow": "明天", "Month": "月", "Week": "週", "This week": "本週", "Next week": "下週", "This month": "本月", "Next month": "下月", "Last quarter": "上季度", "This quarter": "本季度", "Next quarter": "下季度", "This year": "今年", "Next year": "明年", "Last week": "上週", "Last month": "上月", "Last year": "去年", "Last 7 days": "最近 7 天", "Last 30 days": "最近 30 天", "Last 90 days": "最近 90 天", "Next 7 days": "未來 7 天", "Next 30 days": "未來 30 天", "Next 90 days": "未來 90 天", "Work week": "工作日", "Day": "天", "Agenda": "列表", "Date": "日期", "Time": "時間", "Event": "事件", "None": "無", "Unconnected": "未連線", "System settings": "系統設定", "System title": "系統名稱", "Setting": "設定", "Settings": "設定", "Enable": "啟用", "Disable": "停用", "On": "啟用", "Off": "停用", "Logo": "Logo", "Add menu item": "新增選單項目", "Page": "頁面", "Tab": "標籤", "Name": "名稱", "Icon": "圖示", "Group": "群組", "Link": "超連結", "Save conditions": "儲存篩選條件", "Edit menu item": "編輯選單項目", "Move to": "移動到", "Insert left": "從左邊插入", "Insert right": "從右邊插入", "Insert inner": "從裡面插入", "Delete": "刪除", "Disassociate": "解除關聯", "Disassociate record": "解除關聯記錄", "Are you sure you want to disassociate it?": "你確定要解除關聯嗎？", "UI editor": "介面編輯", "Collection": "資料表", "Collection selector": "資料表選擇器", "Providing certain collections as options for users, typically used in polymorphic or inheritance scenarios": "將資料表裡的某些表作為可選項目供給使用者選擇，一般用在多型或繼承的場景裡", "Enable child collections": "啟用子表", "Allow adding records to the current collection": "允許向當前資料表裡新增記錄", "Collections & Fields": "資料表設定", "All collections": "全部資料表", "Add category": "新增分類", "Delete category": "刪除分類", "Edit category": "編輯分類", "Collection category": "資料表類別", "Collection template": "資料表模板", "Visible": "顯示", "Read only": "唯讀", "Easy reading": "唯讀(閱讀模式)", "Hidden": "隱藏", "Hidden(reserved value)": "隱藏(保留數值)", "Not required": "選填", "Value": "欄位數值", "Disabled": "停用", "Enabled": "啟用", "Problematic": "有問題", "Empty": "賦空值", "Linkage rule": "聯動規則", "Linkage rules": "聯動規則", "Condition": "條件", "Properties": "屬性", "Add linkage rule": "新增聯動規則", "Add property": "新增屬性", "Calculation engine": "計算引擎", "Expression": "表達式", "Expression collection": "表達式表", "Sort": "排序", "Categories": "資料表類別", "Category name": "分類名稱", "Add child": "新增子記錄", "Collapse all": "全部收起", "Expand all": "全部展開", "Expand/Collapse": "展開/摺疊", "Default collapse": "預設展開", "Tree collection": "樹狀表", "Tree table": "樹表格", "Parent ID": "父記錄ID", "Parent": "父記錄", "Children": "子記錄", "Roles & Permissions": "角色和權限", "Edit profile": "個人資料", "Change password": "編輯密碼", "Old password": "舊密碼", "New password": "新密碼", "Switch role": "切換角色", "Super admin": "超級管理員", "Language": "語言設定", "Allow sign up": "允許註冊", "Enable SMS authentication": "啟用簡訊登入和註冊", "Sign out": "登出", "Cancel": "取消", "Confirm": "確定", "Submit": "提交", "Close": "關閉", "Set the data scope": "設定資料範圍", "Data loading mode": "資料加載方式", "Set data loading mode": "設定資料加載方式", "Load all data when filter is empty": "當篩選條件為空時加載所有資料", "Do not load data when filter is empty": "當篩選條件為空時不加載資料", "Block": "區塊", "Data blocks": "資料區塊", "Filter blocks": "篩選區塊", "Table": "表格", "Form": "表單", "List": "列表", "Grid Card": "網格卡片", "Screen size": "螢幕尺寸", "pixels": "畫素", "Display title": "顯示標題", "Set the count of columns displayed in a row": "設定一行展示的列數", "Column": "列", "Phone device": "手機裝置", "Tablet device": "平板裝置", "Desktop device": "電腦裝置", "Large screen device": "大螢幕裝置", "Table OID(Inheritance)": "資料表 OID (繼承)", "Collapse": "摺疊", "Select data source": "選擇資料來源", "Calendar": "日曆", "Delete events": "刪除事件", "This event": "此事件", "This and following events": "此事件及後續事件", "All events": "所有事件", "Delete this event?": "是否刪除這個事件？", "Delete Event": "刪除事件", "Kanban": "看板", "Gantt": "甘特圖", "Create gantt block": "建立甘特圖區塊", "Progress field": "進度欄位", "Time scale": "時間縮放等級", "Hour": "小時", "Quarter of day": "四分之一天", "Half of day": "半天", "Year": "年", "QuarterYear": "季度", "Select grouping field": "選擇群組欄位", "Media": "多媒體", "Markdown": "<PERSON><PERSON>", "Wysiwyg": "所見即所得", "Chart blocks": "圖表區塊", "Column chart": "柱狀圖", "Bar chart": "條形圖", "Line chart": "折線圖", "Pie chart": "圓餅圖", "Area chart": "面積圖", "Other chart": "其他圖表", "Other blocks": "其他區塊", "In configuration": "設定中", "Chart title": "圖表標題", "Chart type": "圖表型別", "Chart config": "圖表設定", "Templates": "模板", "Template": "模板", "Select template": "選擇模板", "Action logs": "動作日誌", "Create template": "建立模板", "Edit markdown": "編輯 Markdown", "Add block": "建立區塊", "Add new": "新增", "Add record": "新增資料", "Custom field display name": "自定義欄位名稱", "Display fields": "顯示欄位", "Edit record": "編輯資料", "Delete menu item": "刪除選單項", "Add page": "新增頁面", "Add group": "新增群組", "Add link": "新增連結", "Insert above": "從上方插入", "Insert below": "從下方插入", "Save": "儲存", "Delete block": "刪除區塊", "Are you sure you want to delete it?": "你確定要刪除嗎？", "This is a demo text, **supports Markdown syntax**.": "這是一段演示文字，**支援 Markdown 語法**。", "Filter": "篩選", "Connect data blocks": "連線資料區塊", "Action type": "動作型別", "Actions": "動作", "Update": "更新", "Update record": "更新資料", "Unnamed": "未命名", "View": "檢視", "View record": "檢視資料", "Refresh": "重新整理", "Data changes": "資料變更", "Field name": "欄位標識", "Before change": "變更前", "After change": "變更後", "Delete record": "刪除資料", "Create collection": "建立資料表", "Collection display name": "資料表名稱", "Collection name": "資料表標識", "Inherits": "繼承", "Primary key, unique identifier, self growth": "主键、唯一标识、自增长", "Store the creation user of each record": "記錄建立者", "Store the last update user of each record": "記錄最後更新者", "Store the creation time of each record": "記錄建立時間", "Store the last update time of each record": "記錄最後更新時間", "More options": "更多選項", "Records can be sorted": "可以對行記錄進行排序", "Calendar collection": "日曆資料表", "General collection": "普通資料表", "SQL collection": "SQL資料表", "Connect to database view": "連線資料庫檢視", "Sync from database": "從資料庫同步", "Source collections": "來源資料表", "Field source": "來源欄位", "Preview": "預覽", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.": "隨機生成，可編輯。支援英文、數字和下劃線，必須以英文字母開頭。", "Edit": "編輯", "Edit collection": "編輯資料表", "Configure field": "設定欄位", "Configure fields": "設定欄位", "Configure columns": "設定欄位", "Edit field": "編輯欄位", "Override": "重寫", "Override field": "重寫欄位", "Configure fields of {{title}}": "「{{title}}」的欄位設定", "Association fields filter": "關聯篩選", "PK & FK fields": "主外來鍵欄位", "Association fields": "關聯欄位", "Choices fields": "選項欄位", "System fields": "系統欄位", "General fields": "普通欄位", "Inherited fields": "繼承欄位", "Parent collection fields": "父表欄位", "Basic": "基本型別", "Single line text": "單行文字", "Long text": "多行文字", "Phone": "手機號碼碼", "Email": "電子郵箱", "Username": "使用者名稱", "Null": "空值", "Boolean": "布林值", "Number": "數字", "Integer": "整數", "Percent": "百分比", "String": "字串", "Password": "密碼", "Advanced type": "高階型別", "Formula": "公式", "Formula description": "基於同一條記錄中的其他欄位計算出一個值。", "Syntax references": "語法參考", "Math.js comes with a large set of built-in functions and constants, and offers an integrated solution to work with different data types": "Math.js 包含大量內建函式和常量，並提供了整合的解決方案來處理不同的資料型別。", "Formula.js supports most Microsoft Excel formula functions.": "Formula.js 支援大部分 Mircrosoft Excel 公式。", "Choices": "選擇型別", "Checkbox": "勾選", "Display <icon></icon> when unchecked": "未勾選時顯示 <icon></icon>", "Single select": "下拉選單（單選）", "Multiple select": "下拉選單（多選）", "Radio group": "單選框", "Checkbox group": "選取方塊", "China region": "中國行政區", "Date & Time": "日期 & 時間", "Datetime": "日期", "Relation": "關聯型別", "Link to": "關聯", "Link to description": "用於快速建立表關聯，可相容大多數普通場景。適合非開發人員使用。作為欄位存在時，它是一個下拉選擇用於選擇目標資料表的資料。建立後，將同時在目標資料表中生成當前資料表的關聯欄位。", "Sub-table": "子表格", "System info": "系統資訊", "Created at": "建立日期", "Last updated at": "最後編輯日期", "Created by": "建立者", "Last updated by": "最後編輯者", "Add field": "新增欄位", "Field display name": "欄位名稱", "Field type": "欄位型別", "Field interface": "欄位型別", "Date format": "日期格式", "Year/Month/Day": "年/月/日", "Year-Month-Day": "年-月-日", "Day/Month/Year": "日/月/年", "Show time": "顯示時間", "Time format": "時間格式", "12 hour": "12 小時制", "24 hour": "24 小時制", "Relationship type": "關聯型別", "Inverse relationship type": "反向關聯型別", "Source collection": "來源資料表", "Source key": "來源資料表欄位標識", "Target collection": "目標資料表", "Through collection": "中介資料表", "Target key": "目標資料表欄位標識", "Foreign key": "外來鍵", "One to one": "一對一", "One to many": "一對多", "Many to one": "多對一", "Many to many": "多對多", "Foreign key 1": "外來鍵1", "Foreign key 2": "外來鍵2", "One to one description": "用於建立一對一關聯，比如一個使用者會有一套個人資料。", "One to many description": "用於建立一對多關聯，比如一個國家會有多個城市。作為欄位存在時，它是一個子表格用於顯示目標資料表的資料。建立後，會在目標資料表裡自動生成一個多對一欄位。", "Many to one description": "用於建立多對一關聯，比如一個城市只能屬於一個國家，一個國家可以有多個城市。作為欄位存在時，它是一個下拉選擇用於選擇目標資料表的資料。建立後，會在目標資料表裡自動生成一個多對一欄位。", "Many to many description": "用於建立多對多關聯，比如一個學生會有多個老師，一個老師也會有多個學生。作為欄位存在時，它是一個下拉選擇用於選擇目標資料表的資料。", "Generated automatically if left blank": "留空時，自動生成中介表", "Display association fields": "顯示關聯表的欄位", "Display field title": "顯示欄位標題", "Field component": "欄位元件", "Allow multiple": "允許新增/關聯多條", "Allow dissociate": "允許移除已關聯記錄", "Quick upload": "快速上傳", "Select file": "選擇檔案", "Subtable": "子表格", "Sub-form": "子表單", "Sub-form(Popover)": "子表單 (彈窗)", "Sub-details": "子詳情", "Record picker": "資料選擇器", "Toggles the subfield mode": "切換子欄位模式", "Selector mode": "選擇器模式", "Subtable mode": "子表格模式", "Subform mode": "子表單模式", "Field mode": "欄位元件", "Allow add new data": "允許新增資料", "Edit block title": "編輯區塊標題", "Block title": "區塊標題", "Pattern": "模式", "Operator": "運算子", "Editable": "可編輯", "Readonly": "唯讀", "Easy-reading": "唯讀 (閱讀模式)", "Add filter": "新增篩選條件", "Add filter group": "新增篩選群組", "Comparision": "值比較", "is": "等於", "is not": "不等於", "contains": "包含", "does not contain": "不包含", "starts with": "開頭是", "not starts with": "開頭不是", "ends with": "結尾是", "not ends with": "結尾不是", "is empty": "為空", "is not empty": "不為空", "Edit chart": "編輯圖表", "Add text": "新增文字", "Filterable fields": "可篩選欄位", "Edit button": "編輯按鈕", "Hide": "隱藏", "Enable actions": "啟用動作", "Import": "匯入", "Export": "匯出", "Customize": "自定義", "Custom": "自定義", "Function": "Function", "Popup form": "Popup form", "Flexible popup": "Flexible popup", "Configure actions": "設定動作", "Display order number": "顯示序號", "Enable drag and drop sorting": "啟用拖拽排序", "Triggered when the row is clicked": "點選表格列時觸發", "Add tab": "新增標籤頁", "Disable tabs": "停用標籤頁", "Details": "詳情", "Edit form": "編輯表單", "Create form": "建立表單", "Form (Edit)": "表單（編輯）", "Form (Add new)": "表單（添加）", "Edit tab": "編輯標籤頁", "Relationship blocks": "關聯資料區塊", "Select record": "選擇資料", "Display name": "顯示名稱", "Select icon": "選擇圖示", "Custom column name": "自定義行名稱", "Edit description": "編輯描述", "Required": "必填", "Unique": "不允許重複", "Label field": "標籤欄位", "Default is the ID field": "預設為 ID 欄位", "Set default sorting rules": "設定排序規則", "Set validation rules": "設定驗證規則", "Max length": "最大長度", "Min length": "最小長度", "Maximum": "最大值", "Minimum": "最小值", "Max length must greater than min length": "最大長度必須大於最小長度", "Min length must less than max length": "最小長度必須小於最大長度", "Maximum must greater than minimum": "最大值必須大於最小值", "Minimum must less than maximum": "最小值必須小於最大值", "Validation rule": "驗證規則", "Add validation rule": "新增驗證規則", "Format": "格式", "Regular expression": "正規表達式", "Error message": "錯誤訊息", "Length": "長度", "The field value cannot be greater than ": "數值不能大於", "The field value cannot be less than ": "數值不能小於", "The field value is not an integer number": "數字不是整數", "Set default value": "設定預設值", "Default value": "預設值", "is before": "早於", "is after": "晚於", "is on or after": "不早於", "is on or before": "不晚於", "is between": "介於", "Upload": "上傳", "Select level": "選擇層級", "Province": "省", "City": "市", "Area": "區/縣", "Street": "鄉鎮/街道", "Village": "村/居委會", "Must select to the last level": "必須選到最後一階", "Move {{title}} to": "將 {{title}} 移動到", "Target position": "目標位置", "After": "之後", "Before": "之前", "Add {{type}} before \"{{title}}\"": "在 \"{{title}}\" 前插入{{type}}", "Add {{type}} after \"{{title}}\"": "在 \"{{title}}\" 前插入{{type}}", "Add {{type}} in \"{{title}}\"": "在 \"{{title}}\" 裡插入{{type}}", "Original name": "原名稱", "Custom name": "自定義名稱", "Custom Title": "自定義標題", "Options": "選項", "Option value": "選項值", "Option label": "選項標籤", "Color": "顏色", "Text Align": "文本對齊", "Add option": "新增選項", "Related collection": "關聯表", "Allow linking to multiple records": "允許關聯多條記錄", "Daily": "每天", "Weekly": "每週", "Monthly": "每月", "Yearly": "每年", "Repeats": "重複", "Configure calendar": "設定日曆", "Title field": "標題欄位", "Custom title": "自定義標題", "Show lunar": "展示農曆", "Start date field": "開始日期欄位", "End date field": "結束日期欄位", "Navigate": "分頁", "Title": "標題", "Description": "描述", "Select view": "切換檢視", "Reset": "重置", "Importable fields": "可匯入欄位", "Exportable fields": "可匯出欄位", "Saved successfully": "儲存成功", "Nickname": "暱稱", "Sign in": "登入", "Sign in via account": "帳號密碼登入", "Sign in via phone": "手機號碼登入", "Create an account": "註冊帳號", "Sign up": "註冊", "Confirm password": "確認密碼", "Log in with an existing account": "使用已有帳號登入", "Signed up successfully. It will jump to the login page.": "註冊成功，將跳轉登入頁。", "Password mismatch": "重複密碼不匹配", "Users": "使用者", "Verification code": "驗證碼", "Send code": "傳送驗證碼", "Retry after {{count}} seconds": "{{count}} 秒後重試", "Must be 1-50 characters in length (excluding @.<>\"'/)": "長度為1到50個字元（不能包含@.<>\"'/）", "Roles": "角色", "Add role": "新增角色", "Role name": "角色名稱", "Configure": "設定", "Configure permissions": "設定權限", "Edit role": "編輯角色", "Action permissions": "資料表動作權限", "Menu permissions": "選單訪問權限", "Menu item name": "選單名稱", "Allow access": "允許訪問", "Action name": "動作名稱", "Allow action": "允許動作", "Action scope": "可運算元據範圍", "Operate on new data": "對新增資料動作", "Operate on existing data": "對已有資料動作", "Yes": "是", "No": "否", "Red": "薄暮", "Magenta": "法式洋紅", "Volcano": "火山", "Orange": "日暮", "Gold": "金盞花", "Lime": "青檸", "Green": "極光綠", "Cyan": "<PERSON>青", "Blue": "拂曉藍", "Geek blue": "極客藍", "Purple": "醬紫", "Default": "預設", "Add card": "新增卡片", "edit title": "編輯標題", "Turn pages": "翻頁", "Others": "其他", "Other records": "其他記錄", "Save as reference template": "儲存為引用模板", "Save as inherited template": "儲存為繼承模板", "Save as block template": "儲存為區塊模板", "Block templates": "區塊模板", "Block template": "區塊模板", "Convert reference to duplicate": "模板引用轉為複製", "Template name": "模板名稱", "Block type": "區塊型別", "No blocks to connect": "沒有可連線的區塊", "Action column": "動作行", "Records per page": "每頁顯示數量", "(Fields only)": "（僅欄位）", "Button title": "按鈕標題", "Button icon": "按鈕圖示", "Submitted successfully": "提交成功", "Operation succeeded": "執行成功", "Operation failed": "執行失敗", "Open mode": "開啟方式", "Popup size": "彈窗尺寸", "Small": "較窄", "Middle": "中等", "Large": "較寬", "Size": "大小", "Oversized": "超大", "Auto": "自動", "Object Fit": "適應", "Cover": "覆蓋", "Fill": "填充", "Contain": "包含", "Scale Down": "縮放", "Menu item title": "選單項目名稱", "Menu item icon": "選單項目圖示", "Target": "目標", "Position": "位置", "Insert before": "從前面插入", "Insert after": "從後面插入", "UI Editor": "介面設定", "ASC": "升冪", "DESC": "降冪", "Add sort field": "新增排序欄位", "ID": "ID", "Identifier for program usage. Support letters, numbers and underscores, must start with an letter.": "用於程式使用的識別符號，支援字母、數字和下劃線，必須以字母開頭。", "Drawer": "側欄", "Dialog": "對話方塊", "Delete action": "刪除動作", "Custom column title": "自定義行標題", "Column title": "行標題", "Original title: ": "原始標題: ", "Delete table column": "刪除行", "Skip required validation": "跳過必填檢驗", "Form values": "表單數值", "Fields values": "欄位數值", "The field has been deleted": "欄位已刪除", "When submitting the following fields, the saved values are": "提交以下欄位時，儲存的數值為", "After successful submission": "提交成功後", "Then": "然後", "Stay on current page": "停留在當前頁面", "Redirect to": "跳轉到", "Save action": "儲存動作", "Exists": "存在", "Add condition": "新增條件", "Add condition group": "新增條件群組", "exists": "存在", "not exists": "不存在", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "角色標識", "Precision": "精確度", "Rich Text": "富文字", "Junction collection": "中介表", "Leave it blank, unless you need a custom intermediate table": "預設留空，除非你需要一個自定義的中介表", "Fields": "欄位", "Edit field title": "編輯欄位標題", "Field title": "欄位標題", "Original field title: ": "原始欄位標題：", "Edit tooltip": "編輯提示資訊", "Delete field": "刪除欄位", "Select collection": "選擇資料表", "Blank block": "空區塊", "Duplicate template": "複製模板", "Reference template": "引用模板", "Inherited template": "繼承模板", "Create calendar block": "建立日曆區塊", "Create kanban block": "建立看板區塊", "Grouping field": "群組欄位", "Single select and radio fields can be used as the grouping field": "資料表的單選欄位可以作為群組欄位", "Tab name": "標籤名稱", "Current record blocks": "當前資料區塊", "Popup message": "彈窗提示訊息", "Delete role": "刪除角色", "Role display name": "角色名稱", "Default role": "預設角色", "All collections use general action permissions by default; permission configured individually will override the default one.": "所有資料表都預設使用通用資料動作權限；同時，可以針對每個資料表單獨設定權限。", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "允許設定系統，包括介面設定、資料表設定、權限設定、系統設定等全部設定項", "New menu items are allowed to be accessed by default.": "新增選單項預設允許訪問", "Global permissions": "全域性設定", "General permissions": "通用設定", "Global action permissions": "全域性動作權限", "General action permissions": "通用動作權限", "Plugin settings permissions": "插件設定權限", "Allow to desgin pages": "允許介面設定", "Allow to manage plugins": "允許管理插件", "Allow to configure plugins": "允許管理設定中心", "Allows to configure interface": "允許設定介面", "Allows to install, activate, disable plugins": "允許安裝、啟用、停用插件", "Allows to configure plugins": "允許設定插件", "Action display name": "執行名稱", "Allow": "允許", "Data scope": "資料範圍", "Action on new records": "對新增資料執行", "Action on existing records": "對已有資料執行", "All records": "所有資料", "Own records": "自己的資料", "Permission policy": "權限策略", "Individual": "單獨設定", "General": "通用設定", "Accessible": "允許訪問", "Configure permission": "設定權限", "Action permission": "執行權限", "Field permission": "欄位權限", "Scope name": "資料範圍名稱", "Unsaved changes": "未儲存編輯", "Are you sure you don't want to save?": "你確定不儲存嗎？", "Dragging": "拖拽中", "Popup": "開啟彈窗", "Trigger workflow": "觸發工作流程", "Request API": "請求 API", "Assign field values": "欄位賦值", "Constant value": "靜態值", "Dynamic value": "動態值", "Current user": "當前使用者", "Current role": "當前角色", "Current record": "當前記錄", "Current collection": "當前資料表", "Other collections": "其他資料表", "Current popup record": "當前彈窗記錄", "Parent popup record": "上級彈窗記錄", "Associated records": "關聯記錄", "Parent record": "上級記錄", "Current time": "當前時間", "Now": "現在", "Popup close method": "彈窗關閉方式", "Automatic close": "自動關閉", "Manually close": "手動關閉", "After successful update": "更新成功後", "Save record": "儲存資料", "Updated successfully": "更新成功", "After successful save": "儲存成功後", "After clicking the custom button, the following field values will be assigned according to the following form.": "點選當前自定義按鈕時，以下欄位值將按照以下表單賦值。", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "點選當前自定義按鈕時，當前資料以下欄位將按照以下表單儲存。", "Button background color": "按鈕顏色", "Highlight": "高亮", "Danger red": "紅色", "Custom request": "自定義請求", "Request settings": "請求設定", "Request URL": "請求網址", "Request method": "請求方法", "Request query parameters": "請求查詢引數 (JSON格式)", "Request headers": "請求 Headers (JSON格式)", "Request body": "請求 Body (JSON格式)", "Request success": "請求成功", "Invalid JSON format": "非法 JSON 格式", "After successful request": "請求成功之後", "Add exportable field": "新增可匯出欄位", "Audit logs": "執行記錄", "Record ID": "資料 ID", "User": "使用者", "Field": "欄位", "Select": "選擇", "Select field": "選擇欄位", "Field value changes": "變更記錄", "One to one (has one)": "一對一（has one）", "One to one (belongs to)": "一對一（belongs to）", "Use the same time zone (GMT) for all users": "所有使用者使用同一時區 (格林尼治標準時間)", "Province/city/area name": "省市區名稱", "Enabled languages": "啟用的語言", "View all plugins": "檢視所有插件", "Print": "列印", "Done": "完成", "Sign up successfully, and automatically jump to the sign in page": "註冊成功，即將跳轉到登入頁面", "ACL": "訪問控制", "Collection manager": "資料表管理", "Plugin manager": "插件管理器", "Local": "本地", "Built-in": "內建", "Marketplace": "插件市場", "Add plugin": "新增插件", "Upgrade": "可供更新", "Plugin dependencies check failed": "插件依賴檢查失敗", "Remove": "移除", "Docs": "文件", "More details": "更多詳情", "Upload new version": "上傳新版", "Official plugin": "官方插件", "Version": "版本", "Npm package": "Npm 包", "Upload plugin": "上傳插件", "Npm package name": "Npm 包名", "Add type": "新增方式", "Plugin source": "插件來源", "Changelog": "更新日誌", "Dependencies check": "依賴檢查", "Update plugin": "更新插件", "Installing": "安裝中", "The deletion was successful.": "刪除成功", "Plugin Zip File": "插件壓縮包", "Compressed file url": "壓縮包地址", "Last updated": "最後更新", "PackageName": "包名", "DisplayName": "顯示名稱", "Readme": "說明文件", "Dependencies compatibility check": "依賴相容性檢查", "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.": "插件相容性檢查失敗，你應該編輯依賴版本以滿足版本要求。", "Version range": "版本範圍", "Plugin's version": "插件的版本", "Result": "結果", "No CHANGELOG.md file": "沒有 CHANGELOG.md 日誌", "No README.md file": "沒有 README.md 檔案", "Homepage": "主頁", "Drag and drop the file here or click to upload, file size should not exceed 30M": "將檔案拖放到此處或單擊上傳，檔案大小不應超過 30M", "Dependencies check failed, can't enable.": "依賴檢查失敗，無法啟用。", "Plugin starting...": "插件啟動中...", "Plugin stopping...": "插件停止中...", "Are you sure to delete this plugin?": "確定要刪除此插件嗎？", "re-download file": "重新下載檔案", "Not enabled": "未啟用", "Search plugin": "搜尋插件", "Author": "作者", "Plugin loading failed. Please check the server logs.": "插件載入失敗，請檢查伺服器日誌。", "Coming soon...": "敬請期待...", "All plugin settings": "所有插件設定", "Bookmark": "書籤", "Manage all settings": "管理所有設定", "Create inverse field in the target collection": "在目標資料表裡建立反向關聯欄位", "Inverse field name": "反向關聯欄位標識", "Inverse field display name": "反向關聯欄位名稱", "Bulk update": "批次更新", "After successful bulk update": "批次成功更新後", "Bulk edit": "批次編輯", "Data will be updated": "更新的資料", "Selected": "選中", "All": "所有", "Update selected data?": "更新選中的資料嗎？", "Update all data?": "更新全部資料嗎？", "Remains the same": "不更新", "Changed to": "編輯為", "Clear": "清空", "Add attach": "增加關聯", "Please select the records to be updated": "請選擇要更新的記錄", "Selector": "選擇器", "Inner": "裡面", "Search and select collection": "搜尋並選擇資料表", "Please fill in the iframe URL": "請填寫嵌入的地址", "Fix block": "固定區塊", "Plugin name": "插件", "Plugin tab name": "插件標籤頁", "Enable page header": "啟用頁首", "Display page title": "顯示頁面標題", "Edit page title": "編輯頁面標題", "Enable page tabs": "啟用頁面選項卡", "Enable link": "啟用連結", "Column width": "列寬", "Sortable": "可排序的", "Constant": "常量", "Select a variable": "選擇變數", "Insert": "插入", "Insert if not exists": "不存在時插入", "Insert if not exists, or update": "不存在時插入，否則更新", "System variables": "系統變數", "Date variables": "日期變數", "Double click to choose entire object": "雙擊選擇整個物件", "True": "True", "False": "False", "Prettify": "格式化", "Theme": "主題", "Default theme": "預設主題", "Compact theme": "緊湊主題", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "這可能是 NocoBase 內部的問題，你可以在<1>這裡</1>將該錯誤反饋給我們，我們會盡快修復", "Render Failed": "渲染失敗", "Feedback": "反饋問題", "Try again": "重試一下", "Click or drag file to this area to upload": "點選或拖拽檔案到此區域上傳", "Support for a single or bulk upload, file size should not exceed": "支援單個或批次上傳，檔案大小不能超過", "Default title for each record": "用作資料的預設標題", "If collection inherits, choose inherited collections as templates": "當前表有繼承關聯時，可選擇繼承鏈路上的表作為模板來源", "Select an existing piece of data as the initialization data for the form": "選擇一條已有的資料作為表單的初始化資料", "Only the selected fields will be used as the initialization data for the form": "僅選擇的欄位才會作為表單的初始化資料", "Template Data": "模板資料", "Data fields": "資料欄位", "Add template": "新增模板", "Display data template selector": "顯示資料模板選擇框", "Form data templates": "表單資料模板", "Data template": "資料模板", "No configuration available.": "無可設定項。", "Reload application": "過載 App", "The application is reloading, please do not close the page.": "App 正在重新載入，請勿關閉頁面。", "Application reloading": "App 重新載入中", "Restart application": "重啟 App", "Allows to clear cache, reboot application": "允許清除快取，重啟 App", "The will interrupt service, it may take a few seconds to restart. Are you sure to continue?": "重啟將會中斷當前服務，這個過程可能需要一點時間，確定要繼續嗎？", "Restart": "重啟", "Clear cache": "清除快取", "Duplicate": "複製", "Duplicating": "複製中", "Duplicate mode": "複製方式", "Quick duplicate": "快速複製", "Duplicate and continue": "複製並繼續", "Please configure the duplicate fields": "請設定要複製的欄位", "Add": "建立", "Add new mode": "新增方式", "Quick add": "快捷新增", "Modal add": "彈窗新增", "Save mode": "儲存方式", "First or create": "不存在時則新增,存在時不處理", "Update or create": "不存在時新增,存在時更新", "Find by the following fields": "透過以下欄位查詢", "Create": "僅新增", "Current form": "當前表單", "Current object": "當前物件", "Quick create": "快速建立", "Dropdown": "下拉選單", "Pop-up": "彈窗", "File manager": "檔案管理器", "Direct duplicate": "直接複製", "Copy into the form and continue to fill in": "複製到表單並繼續填寫", "Linkage with form fields": "從表單欄位聯動", "App error": "App 錯誤", "Failed to load plugin": "插件載入失敗", "Allow add new, update and delete actions": "允許增刪改動作", "Date display format": "日期顯示格式", "Assign  data scope for the template": "為模板指定資料範圍", "Table selected records": "表格中選中的記錄", "Tag": "標籤", "Tag color field": "標籤顏色欄位", "Sync successfully": "同步成功", "Sync from form fields": "同步表單欄位", "Select all": "全選", "UnSelect all": "取消全選", "Determine whether a record exists by the following fields": "透過以下欄位判斷記錄是否存在", "Cascade Select": "級聯選擇", "Execute": "執行", "Please use a valid SELECT or WITH AS statement": "請使用有效的 SELECT 或 WITH AS 語句", "Please confirm the SQL statement first": "請先確認 SQL 語句", "Automatically drop objects that depend on the collection (such as views), and in turn all objects that depend on those objects": "自動刪除依賴於該表的物件,以及依賴這些物件的物件", "Secondary confirmation": "再次確認", "Perform the {{title}}": "執行{{title}}", "Are you sure you want to perform the {{title}} action?": "你確定執行{{title}}動作嗎？", "Sign in with another account": "登入其他帳號", "Return to the main application": "返回主App", "Permission denied": "沒有權限", "Allow add new": "允許新增", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "{{type}} \"{{name}}\" 可能已被刪除，請移除這個 {{blockType}}。", "data source": "數據源", "Data source": "數據源", "DataSource": "數據源", "Allow selection of existing records": "允許選擇已有資料", "Home page": "主頁", "Handbook": "使用手冊", "License": "許可證", "This variable has been deprecated and can be replaced with \"Current form\"": "該變數已被棄用，可以使用“當前表單”作為替代", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "該變數的值來自頁面 URL 的查詢字符串，只有當頁面有查詢字符串時，該變數才能正常使用。", "URL search params": "URL 查詢參數", "Expand All": "展開全部", "Clear default value": "清除預設值", "Open in new window": "新窗口打開", "Sorry, the page you visited does not exist.": "抱歉，你訪問的頁面不存在。", "Allow multiple selection": "允許多選", "Parent object": "上級物件", "Ellipsis overflow content": "省略超出長度的內容", "Hide column": "隱藏列", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "在配置模式下，整個列會變為透明色。在非配置模式下，整個列將被隱藏。即使整個列被隱藏了，其配置的默認值和其他設置仍然有效。", "Show file name": "显示文件名", "Desktop routes": "桌面端路由", "Route permissions": "路由權限", "New routes are allowed to be accessed by default": "新路由默認允許訪問", "Route name": "路由名稱", "Mobile routes": "移動端路由", "Show in menu": "在菜單中顯示", "Hide in menu": "在菜單中隱藏", "Path": "路徑", "Type": "類型", "Access": "訪問", "Routes": "路由", "Add child route": "添加子路由", "Delete routes": "刪除路由", "Delete route": "刪除路由", "Are you sure you want to hide these routes in menu?": "你確定要在菜單中隱藏這些路由嗎？", "Are you sure you want to show these routes in menu?": "你確定要在菜單中顯示這些路由嗎？", "Are you sure you want to hide this menu?": "你確定要隱藏這個菜單嗎？", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "隱藏後，這個菜單將不再出現在菜單欄中。要再次顯示它，你需要到路由管理頁面進行設置。", "If selected, the page will display Tab pages.": "如果選中，該頁面將顯示標籤頁。", "If selected, the route will be displayed in the menu.": "如果選中，該路由將顯示在菜單中。", "Are you sure you want to hide this tab?": "你確定要隱藏這個標籤嗎？", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "隱藏後，這個標籤將不再出現在標籤欄中。要再次顯示它，你需要到路由管理頁面進行設置。", "Deprecated": "已棄用", "Full permissions": "完全權限", "Refresh data blocks": "刷新數據區塊", "Select data blocks to refresh": "選擇要刷新的數據區塊", "After successful submission, the selected data blocks will be automatically refreshed.": "提交成功後，選中的數據區塊將自動刷新。", "No pages yet, please configure first": "尚未配置頁面，請先配置", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "點擊右上角的 \"介面設定\" 圖示進入介面設定模式", "Reset link expiration": "重置連結過的期時間"}