{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "各ページ<1><0>10</0><1>20</1><2>50</2><3>100</3></1> 件表示", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "グループ内の<1><0>すべて</0><1>一部</1></1> の条件を満たす", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "<1><0>モーダル</0><1>ドロワー</1><2>ウィンドウ</2></1>で開く", "{{count}} filter items": "{{count}} つのフィルター項目", "{{count}} more items": "{{count}} 件以上", "Total {{count}} items": "合計 {{count}} 件", "Today": "今日", "Yesterday": "昨日", "Tomorrow": "明日", "Month": "月", "Week": "週", "This week": "今週", "Next week": "来週", "This month": "今月", "Next month": "来月", "Last quarter": "前四半期", "This quarter": "今四半期", "Next quarter": "来四半期", "This year": "今年", "Next year": "来年", "Last week": "先週", "Last month": "先月", "Last year": "去年", "Last 7 days": "過去 7 日間", "Last 30 days": "過去 30 日間", "Last 90 days": "過去 90 日間", "Next 7 days": "次の 7 日間", "Next 30 days": "次の 30 日間", "Next 90 days": "次の 90 日間", "Work week": "稼働日", "Day": "日", "Agenda": "アジェンダ", "Date": "日付", "Time": "時間", "Event": "イベント", "None": "なし", "Unconnected": "未接続", "System settings": "システム設定", "System title": "システム名", "Logo": "ロゴ", "Add menu item": "メニュー項目を追加", "Page": "ページ", "Tab": "タブ", "Name": "名称", "Icon": "アイコン", "Group": "グループ", "Link": "リンク", "Save conditions": "条件を保存", "Edit menu item": "メニュー項目を編集", "Move to": "移動", "Insert left": "左に挿入", "Insert right": "右に挿入", "Insert inner": "中に挿入", "Delete": "削除", "Disassociate": "関連付けを解除", "Disassociate record": "レコードの関連付けを解除", "Are you sure you want to disassociate it?": "本当に関連付けを解除しますか？", "UI editor": "UI エディタ", "Collection": "コレクション", "Enable child collections": "启用子表", "Allow adding records to the current collection": "現在のデータ・フォームへのレコードの追加を許可する", "Collections & Fields": "コレクションとフィールド", "All collections": "すべてのデータテーブル", "Add category": "分類の追加", "Edit category": "分類の編集", "Sort": "ソート＃ソート＃", "Categories": "データテーブルカテゴリ", "Category name": "分類名", "Delete category": "分類の削除", "Collection category": "Collection category", "Add child": "サブレコードの追加", "Collapse all": "すべて閉じる", "Expand all": "すべて展開", "Expand/Collapse": "展開と終了", "Tree table": "ツリーテーブル", "Visible": "表示", "Read only": "読み取り専用(編集不可)", "Easy reading": "読み取り専用(読取りモード)", "Hidden": "非表示", "Hidden(reserved value)": "非表示(値の保持)", "Not required": "必須ではありません", "Value": "値", "Disabled": "無効化", "Enabled": "有効化", "On": "有効化", "Off": "無効化", "Empty": "くうきち", "Linkage rule": "連動規則", "Linkage rules": "連動規則", "Condition": "条件＃ジョウケン＃", "Properties": "属性＃ゾクセイ＃", "Add linkage rule": "連動規則の追加", "Add property": "属性の追加", "Roles & Permissions": "役割と権限", "Edit profile": "プロフィール", "Change password": "パスワード変更", "Old password": "現在のパスワード", "New password": "新しいパスワード", "Switch role": "役割の切替", "Super admin": "管理者", "Language": "言語", "Allow sign up": "サインアップを許可", "Sign out": "サインアウト", "Cancel": "取消", "Submit": "保存", "Close": "閉じる", "Set the data scope": "データ範囲の設定", "Data loading mode": "データ読み込みモード", "Set data loading mode": "データ読み込みモードの設定", "Load all data when filter is empty": "フィルターが空の場合、すべてのデータを読み込む", "Do not load data when filter is empty": "フィルターが空の場合、データを読み込まない", "Data blocks": "データブロック", "Filter blocks": "フィルターブロック", "Table OID(Inheritance)": "データテーブルOID(継承)", "Table": "テーブル", "Form": "フォーム", "Collapse": "折りたたみ", "Select data source": "データソースを選択", "Calendar": "カレンダー", "Kanban": "かんばん", "Gantt": "ガント図", "Create gantt block": "ガントチャートブロックの作成", "Progress field": "進捗フィールド", "Time scale": "時間スケールレベル", "Hour": "時間", "Quarter of day": "四分の一日", "Half of day": "半日", "Year": "年", "QuarterYear": "四半期", "Select grouping field": "グループフィールドを選択してください", "Media": "メディア", "Markdown": "マークダウン", "Wysiwyg": "Wysiwyg", "Chart blocks": "チャートブロック", "Column chart": "縦棒グラフ", "Bar chart": "横棒グラフ", "Line chart": "折れ線グラフ", "Pie chart": "円グラフ", "Area chart": "積み上げ面グラフ", "Other chart": "その他のグラフ", "Other blocks": "その他のブロック", "In configuration": "設定中", "Chart title": "チャートタイトル", "Chart type": "チャートタイプ", "Chart config": "チャート設定", "Templates": "テンプレート", "Template": "テンプレート", "Select template": "テンプレートを選択してください", "Action logs": "操作履歴", "Create template": "テンプレートを作成", "Edit markdown": "マークダウンを編集", "Add block": "ブロックを追加", "Add new": "追加", "Add record": "レコードを追加", "Custom field display name": "カスタムフィールド名", "Display fields": "表示フィールド", "Edit record": "レコードを編集", "Delete menu item": "メニュー項目を削除", "Add page": "ページを追加", "Add group": "グループを追加", "Add link": "リンクを追加", "Insert above": "上に挿入", "Insert below": "下に挿入", "Save": "保存", "Delete block": "ブロックを削除", "Are you sure you want to delete it?": "本当に削除しますか？", "This is a demo text, **supports Markdown syntax**.": "これはデモテキストです。 **マークダウン構文をサポートしています。**", "Filter": "フィルター", "Connect data blocks": "データブロックを連結", "Action type": "操作タイプ", "Actions": "操作", "Insert": "作成", "Update": "更新", "View": "表示", "View record": "レコードを見る", "Refresh": "リフレッシュ", "Data changes": "データ変更", "Field name": "フィールド識別子", "Before change": "変更前", "After change": "変更後", "Delete record": "レコード削除", "Delete collection": "データテーブルの削除", "Create collection": "コレクションの作成", "Collection display name": "コレクション名", "Collection name": "コレクション識別子", "Inherits": "継承", "AutoGenId": "IDフィールドの自動生成", "CreatedBy": "レコード作成者", "UpdatedBy": "レコード最終更新者", "CreatedAt": "レコード作成時間", "UpdatedAt": "レコード最終更新時間", "Records can be sorted": "ソート可能", "Collection template": "データテーブルテンプレート", "Calendar collection": "カレンダデータテーブル", "General collection": "一般データテーブル", "Connect to database view": "ビューに接続", "Source collections": "ソースデータセット", "Field source": "ソースフィールド", "Preview": "プレビュー", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.": "ランダムに生成され、変更可能です。 アルファベット、数字、アンダースコアをサポートし、アルファベットから始まる必要があります。", "Edit": "編集", "Edit collection": "コレクションの編集", "Configure fields": "フィールドの設定", "Configure columns": "カラムの設定", "Edit field": "フィールドの編集", "Override": "書き換え", "Override field": "フィールドの上書き", "Configure fields of {{title}}": "{{title}}のフィールド設定", "Basic": "基本タイプ", "Single line text": "一行テキスト", "Long text": "長文テキスト", "Phone": "電話番号", "Email": "メール", "Number": "数値", "Integer": "整数", "Percent": "パーセント", "Password": "パスワード", "Advanced type": "アドバンスタイプ", "Formula": "式", "Formula description": "各レコードの値を、同じレコード内の他のフィールドを基に計算します。", "Choices": "選択", "Checkbox": "チェックボックス", "Single select": "ドロップダウン（単数選択）", "Multiple select": "ドロップダウン（複数選択）", "Radio group": "ラジオボタングループ", "Checkbox group": "チェックボックスグループ", "China region": "中国地域", "Date & Time": "日付と時間", "Datetime": "日付", "Relation": "関連づけ", "Link to": "リンク", "Link to description": "コレクションの関連付けを素早く作成するためにしようされ、ほとんどの一般的なシナリオに対応しています。開発者以外の方のしようにも適しています。フィールドとして存在する場合、参照元コレクションのレコードを選択するために使用されるドロップダウンです。一度作成されると、参照先コレクションに現在のコレクションの関連フィールドが同時に生成されます。", "Sub-table": "サブテーブル", "Sub-form(Popover)": "サブフォーム（ポップアップ窓）", "System info": "システム情報", "Created at": "作成日", "Last updated at": "最終更新日", "Created by": "作成者", "Last updated by": "最終更新者", "Add field": "フィールドの追加", "Field display name": "フィールド表示名", "Field type": "フィールドタイプ", "Field interface": "フィールドタイプ", "Date format": "日付の書式", "Year/Month/Day": "年/月/日", "Year-Month-Day": "年-月-日", "Day/Month/Year": "日/月/年", "Show time": "時刻を表示", "Time format": "時間形式", "12 hour": "12 時間制", "24 hour": "24 時間制", "Relationship type": "関連付けタイプ", "Source collection": "参照元コレクション", "Source key": "参照元キー", "Target collection": "参照先コレクション", "Through collection": "中間コレクション", "Target key": "参照先キー", "Foreign key": "外部キー", "One to one": "1対1", "One to many": "1対多", "Many to one": "多対1", "Many to many": "多対多", "One to one description": "1対1の関係を作成するために使用します。例えば、1つのユーザーは1つのプロファイルを持つことになります。", "One to many description": "1対多の関係を作成するために使用します。例えば、国には多くの都市があり、都市は1つの国にしか存在できません。フィールドとして存在する場合、それは参照先コレクションのレコードを表示するために使用されるサブテーブルです。作成されると、多対一のフィールドが参照先コレクションに自動的に生成されます。", "Many to one description": "これは多対一の関係を作るために使われます。例えば、都市は1つの国にしか属さず、国は複数の都市を持つことができます。 フィールドとして存在する場合、参照先コレクションのレコードを選択するために使用されるドロップダウンです。 一度作成されると、対象の参照先コレクションに多対一のフィールドが自動的に生成されます。", "Many to many description": "これは多対多の関係を作成するために使用されます。例えば、生徒は複数の教師を持ち、教師は複数の生徒を持つことになります。 フィールドとして存在する場合、参照先コレクションのレコードを選択するために使用されるドロップダウン選択です。", "Foreign key 1": "外部キー1", "Foreign key 2": "外部キー2", "Add filter": "フィルターを追加", "Add filter group": "フィルターグループを追加", "Comparision": "比較", "is": "が同じである", "is not": "が同じではない", "contains": "を含む", "does not contain": "を含まない", "starts with": "で始まる", "not starts with": "で始まらない", "ends with": "で終わる", "not ends with": "で終わらない", "is empty": "が空である", "is not empty": "が空ではない", "Edit chart": "チャートを編集", "Add text": "テキストを追加", "Filterable fields": "フィルタリング可能なフィールド", "Edit button": "ボタンを編集", "Hide": "隠す", "Enable actions": "有効な操作", "Export": "エクスポート", "Customize": "カスタマイズ", "Function": "Function", "Popup form": "Popup form", "Flexible popup": "Flexible popup", "Configure actions": "操作の設定", "Display order number": "シリアルナンバーを表示", "Enable drag and drop sorting": "ドラッグアンドドロップによる並び替えを有効にする", "Triggered when the row is clicked": "行がクリックされたときトリガーされます", "Add tab": "タブを追加", "Disable tabs": "タブを無効にする", "Details": "詳細", "Edit form": "フォームを編集", "Create form": "フォームを作成", "Form (Edit)": "フォーム (編集)", "Form (Add new)": "フォーム (新規追加)", "Edit tab": "タブを編集", "Relationship blocks": "関連付けされたブロック", "Select record": "レコードを選択", "Display name": "表示名", "Select icon": "アイコンを選択してください", "Custom column name": "カスタムカラム名", "Edit description": "説明を編集", "Required": "必須", "Label field": "ラベルフィールド", "Default is the ID field": "デフォルトはIDフィールド", "Set default sorting rules": "デフォルトのソートルールを設定", "is before": "より前", "is after": "より後", "is on or after": "以降", "is on or before": "以前", "is between": "範囲", "Upload": "アップロード", "Select level": "レベルを選択", "Province": "州", "City": "市", "Area": "地区/群", "Street": "町/通り", "Village": "村", "Must select to the last level": "最後のレベルまで選択する必要があります", "Move {{title}} to": "{{title}} を移動", "Target position": "ターゲットの位置", "After": "後", "Before": "前", "Add {{type}} before \"{{title}}\"": "\"{{title}}\"の前に{{type}}を挿入", "Add {{type}} after \"{{title}}\"": "\"{{title}}\"の後に{{type}}を挿入", "Add {{type}} in \"{{title}}\"": "\"{{title}}\" に {{type}} を挿入", "Original name": "元の名称", "Custom name": "カスタム名", "Custom Title": "カスタムタイトル", "Options": "オプション", "Option value": "オプション 値", "Option label": "オプション ラベル", "Color": "カラー", "Add option": "オプションを追加", "Related collection": "関連付けコレクション", "Allow linking to multiple records": "複数のレコードの関連付けを許可する", "Configure calendar": "カレンダーの設定", "Title field": "タイトルフィールド", "Start date field": "開始日フィールド", "End date field": "終了日フィールド", "Navigate": "ページネーション", "Title": "タイトル", "Description": "説明", "Select view": "ビューの切り替え", "Reset": "リセット", "Exportable fields": "エクスポート可能なフィールド", "Saved successfully": "保存に成功しました", "Nickname": "ニックネーム", "Sign in": "サインイン", "Create an account": "アカウント登録", "Sign up": "サインアップ", "Confirm password": "パスワードを確認", "Log in with an existing account": "既存のアカウントでログイン", "Signed up successfully. It will jump to the login page.": "登録に成功すると、ログインページにリダイレクトされます", "Password mismatch": "パスワードが一致しません", "Users": "ユーザー", "Roles": "役割", "Add role": "役割の追加", "Role name": "役割名", "Configure": "設定", "Configure permissions": "権限の設定", "Edit role": "ロールを編集", "Action permissions": "コレクションの操作権限", "Menu permissions": "メニューアクセス権限", "Menu item name": "メニュー項目名", "Allow access": "許可されたアクセス", "Action name": "操作名", "Allow action": "許可された操作", "Action scope": "操作可能なレコード範囲", "Operate on new data": "新規データを操作する", "Operate on existing data": "既存のデータを操作する", "Yes": "はい", "No": "いいえ", "Red": "レッド", "Magenta": "マゼンタ", "Volcano": "ボルケーノ", "Orange": "オレンジ", "Gold": "ゴールド", "Lime": "ライム", "Green": "グリーン", "Cyan": "シアン", "Blue": "ブルー", "Geek blue": "ギークブルー", "Purple": "パープル", "Default": "デフォルト", "Add card": "カードを追加", "edit title": "タイトルを編集", "Turn pages": "ページをめくる", "Others": "その他", "Other records": "他のレコード", "Save as reference template": "参照テンプレートとして保存", "Save as inherited template": "継承テンプレートとして保存", "Save as block template": "ブロックテンプレートとして保存", "Block templates": "ブロックテンプレート", "Block template": "ブロックテンプレート", "Convert reference to duplicate": "参照を複製に変換", "Template name": "テンプレート名", "Block type": "ブロックタイプ", "No blocks to connect": "接続するブロックがありません", "Action column": "操作カラム", "Records per page": "ページごとのレコード数", "(Fields only)": "(フィールドのみ)", "Button title": "ボタンタイトル", "Button icon": "ボタンアイコン", "Submitted successfully": "正常に送信されました", "Operation succeeded": "操作が成功しました", "Operation failed": "操作に失敗しました", "Open mode": "オープンモード", "Menu item title": "メニュー項目名", "Menu item icon": "メニュー項目アイコン", "Target": "ターゲット", "Position": "位置", "Insert before": "前に挿入", "Insert after": "後ろに挿入", "UI Editor": "UI エディタ", "ASC": "昇順", "DESC": "降順", "Add sort field": "ソートフィールドを追加", "ID": "ID", "Drawer": "ドロワー", "Dialog": "ダイアログ", "Delete action": "操作を削除", "Custom column title": "カスタムカラムタイトル", "Column title": "カラムタイトル", "Original title: ": "元のタイトル: ", "Delete table column": "テーブルのカラムを削除", "Skip required validation": "必須のバリデーションをスキップ", "Form values": "フォームの値", "Fields values": "フィールドの値", "The field has been deleted": "フィールドが削除されました", "When submitting the following fields, the saved values are": "次のフィールドを送信すると、保存された値は", "After successful submission": "送信が成功した後", "Then": "その後", "Stay on current page": "現在のページにとどまる", "Redirect to": "リダイレクトする", "Save action": "操作を保存", "Exists": "存在する", "Add condition": "条件の追加", "Add condition group": "条件グループの追加", "exists": "が存在する", "not exists": "が存在しない", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "役割ID", "Precision": "精度", "Formula mode": "数式モード", "Expression": "表达式", "Input +, -, *, /, ( ) to calculate, input @ to open field variables.": "+、-、*、/、( ) で算術演算、@でフィールド変数を開くことができます。", "Formula error.": "式の検証エラーです。", "Rich Text": "リッチテキスト", "Junction collection": "中間コレクション", "Leave it blank, unless you need a custom intermediate table": "カスタム中間テーブルが必要でない限り、デフォルトで空白のままにします", "Fields": "フィールド", "Edit field title": "フィールドタイトルを編集", "Field title": "フィールドタイトル", "Original field title: ": "元のフィールドタイトル: ", "Edit tooltip": "ツールチップを編集", "Delete field": "フィールドを削除", "Select collection": "コレクションを選択してください", "Blank block": "空のブロック", "Duplicate template": "テンプレートをコピー", "Reference template": "テンプレートを参照", "Inherited template": "継承テンプレート", "Create calendar block": "カレンダーブロックの作成", "Create kanban block": "かんばんブロックの作成", "Grouping field": "グループフィールド", "Tab name": "タブ名", "Current record blocks": "現在のレコードブロック", "Popup message": "ポップアップメッセージ", "Delete role": "役割を削除", "Role display name": "役割名", "Default role": "デフォルトの役割", "All collections use general action permissions by default; permission configured individually will override the default one.": "すべてのコレクションは、デフォルトで一般操作権限を使用します。 各コレクションに対して個別に権限を設定することができます。", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "UI設定、コレクション設定、権限設定、システム設定等、システム全体の設定を許可する", "New menu items are allowed to be accessed by default.": "新しいメニュー項目を追加すると、デフォルトでアクセスが許可されます", "Global permissions": "グローバル設定", "General permissions": "一般設定", "Global action permissions": "グローバル操作権限", "General action permissions": "一般操作権限", "Plugin settings permissions": "中央権限の設定", "Allow to desgin pages": "インタフェース構成の許可", "Allow to manage plugins": "管理プラグインの許可", "Allow to configure plugins": "管理構成センターの許可", "Action display name": "操作名", "Allow": "許可する", "Data scope": "レコードスコープ", "Action on new records": "新しいレコードに対する操作", "Action on existing records": "既存のレコードに対する操作", "All records": "すべてのレコード", "Own records": "自身が所有するレコード", "Permission policy": "権限ポリシー", "Individual": "個別設定", "General": "一般設定", "Accessible": "アクセスを許可する", "Configure permission": "権限設定", "Action permission": "操作権限", "Field permission": "フィールド権限", "Scope name": "スコープ名", "Unsaved changes": "変更が保存されていません", "Are you sure you don't want to save?": "変更を保存しなくてもよいですか?", "Dragging": "ドラッグ", "Popup": "ポップアップ", "Trigger workflow": "トリガーワークフロー", "Request API": "リクエスト API", "Assign field values": "フィールド割当", "Constant value": "定数値", "Dynamic value": "動的値", "Current user": "現在のユーザー", "Current role": "現在の役割", "Current record": "現在のレコード", "Current collection": "現在のコレクション", "Other collections": "他のコレクション", "Current popup record": "現在のポップアップレコード", "Parent popup record": "親ポップアップレコード", "Associated records": "関連付けられたレコード", "Popup close method": "ポップアップを閉じる方法", "Automatic close": "自動で閉じる", "Manually close": "手動で閉じる", "After successful update": "更新成功後", "Save record": "レコードを保存", "Updated successfully": "更新成功", "After successful save": "保存に成功した後", "After clicking the custom button, the following field values will be assigned according to the following form.": "現在のカスタムボタンをクリックすると、次のフォームに従って次のフィールド値が割り当てられます。", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "カスタムボタンをクリックすると、現在のレコードの次のフィールドが次の形式に従って保存されます。", "Button background color": "ボタンの背景色", "Highlight": "ハイライト", "Danger red": "赤", "Custom request": "カスタムリクエスト", "Request settings": "リクエスト設定", "Request URL": "リクエスト URL", "Request method": "リクエスト メソッド", "Request query parameters": "リクエスト クエリ パラメータ", "Request headers": "リクエスト ヘッダ", "Request body": "リクエスト ボディ", "Request success": "リスエスト成功", "Invalid JSON format": "不正なJSONフォーマット", "After successful request": "リクエストが成功した後", "Add exportable field": "エクスポート可能なフィールドを追加", "Audit logs": "監査ログ", "Record ID": "レコード ID", "User": "ユーザー", "Field": "フィールド", "Field value changes": "変更履歴", "One to one (has one)": "1対1 (has one)", "One to one (belongs to)": "1対1 (belongs to)", "Use the same time zone (GMT) for all users": "すべてのユーザーが同じタイムゾーン（GMT）を使用する", "Block title": "ブロックタイトル", "Edit block title": "ブロックタイトルを編集", "Province/city/area name": "Province/city/area name", "Field component": "フィールドコンポーネント", "Allow multiple": "複数選択を許可する", "Quick upload": "クイックアップロード", "Select file": "ファイルを選択", "Subtable": "サブテーブル", "Sub-form": "サブフォーム", "Sub-details": "サブリスト", "Field mode": "フィールドコンポーネント", "Allow add new data": "データの追加を許可", "Regular expression": "正規表現", "Enabled languages": "利用可能な言語", "View all plugins": "すべてのプラグラインを見る", "Print": "Print", "Single select and radio fields can be used as the grouping field": "単一選択フィールドとラジオ フィールドをグループ化フィールドとして使用できます", "Sign up successfully, and automatically jump to the sign in page": "アカウントの登録に成功すると、ログインページにリダイレクトされます", "Unique": "重複を許可しない", "Default value": "デフォルト値", "Select field": "フィールドを選択してください", "Any succeeded or failed": "いずれかが成功もしくは失敗", "Continue after any branch succeeded, or exit after any branch failed": "いずれかの分岐で成功し続行するか、分岐が失敗した後終了します", "Delay": "遅延実行", "Duration": "間隔", "End Status": "終了状態", "Select status": "状態を選択してください", "Succeed and continue": "成功し続行", "Fail and exit": "失敗し終了", "Enable SMS authentication": "SMS認証を有効にする", "Display association fields": "関連付けられたコレクションのフィールドを表示", "Set default value": "デフォルト値を設定", "Choices fields": "チョイスフィールド", "Editable": "編集可能", "Readonly": "読み取り専用（編集不可）", "Easy-reading": "読取り専用（読取りモード）", "Set validation rules": "バリデーションルールの設定", "Add validation rule": "バリデーションルールを追加", "Validation rule": "バリデーションルール", "Maximum": "最大值", "Minimum": "最小值", "Max length must greater than min length": "最大値は最小値より大きくなくてはなりません", "Min length must less than max length": "最小値は最大値より小さくなくてはなりません", "Maximum must greater than minimum": "最大値は最小値より大きくなければなりません", "Minimum must less than maximum": "最小値は最大値より小さくなければなりません", "Format": "フォーマット", "Error message": "エラーメッセージ", "Record picker": "レコードピッカー", "Search and select collection": "コレクションを検索して選択", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "これはNocoBaseの内部バグの可能性があります。 <1>こちら</1>で問題を報告してください", "Render Failed": "レンダリングに失敗しました", "Feedback": "フィードバック", "Try again": "再試行", "Click or drag file to this area to upload": "クリックまたはドラッグしてファイルをアップロード", "Support for a single or bulk upload, file size should not exceed": "単一または複数のファイルをアップロードできます。ファイルサイズは", "Default title for each record": "各レコードのデフォルトタイトル", "If collection inherits, choose inherited collections as templates": "コレクションが継承されている場合、継承されたコレクションをテンプレートとして選択してください", "Select an existing piece of data as the initialization data for the form": "既存のデータを選択して、フォームの初期化データとして使用します", "Only the selected fields will be used as the initialization data for the form": "選択したフィールドのみがフォームの初期化データとして使用されます", "Template Data": "テンプレートデータ", "Data fields": "データフィールド", "Add template": "テンプレートを追加", "Display data template selector": "データテンプレートセレクターを表示", "Form data templates": "フォームデータテンプレート", "Data template": "データテンプレート", "Duplicate": "レプリケーション", "Duplicating": "コピー中", "Duplicate mode": "コピーモード", "Quick duplicate": "今すぐコピー", "Duplicate and continue": "コピーして続行", "Please configure the duplicate fields": "コピーするフィールドを設定してください", "Add": "追加", "Add new mode": "追加モード", "Quick add": "すばやい", "Modal add": "ポップアップ窓の追加", "Save mode": "保存方法", "First or create": "存在しない場合に追加", "Update or create": "存在しなければ新規、存在すれば更新", "Find by the following fields": "次のフィールドで検索", "Create": "新規のみ", "Current form": "現在のフォーム", "Current object": "現在のオブジェクト", "Linkage with form fields": "フォームデータから連動", "Allow add new, update and delete actions": "削除変更操作の許可", "Date display format": "日付表示形式", "Assign  data scope for the template": "テンプレートのデータ範囲の指定", "Tag": "タブ", "Tag color field": "ラベルの色フィールド", "Sync successfully": "同期成功", "Sync from form fields": "フォームフィールドの同期", "Select all": "すべて選択", "Cascade Select": "カスケード選択", "New plugin": "新しいプラグイン", "Upgrade": "アップグレード", "Dependencies check failed": "依存関係のチェックに失敗しました", "More details": "詳細", "Upload new version": "新しいバージョンをアップロード", "Version": "バージョン", "Npm package": "Npmパッケージ", "Npm package name": "Npmパッケージ名", "Upload plugin": "プラグインをアップロード", "Official plugin": "公式プラグイン", "Add type": "タイプを追加", "Changelog": "変更履歴", "Dependencies check": "依存関係のチェック", "Update plugin": "プラグインをアップグレード", "Installing": "インストール中", "The deletion was successful.": "削除に成功しました。", "Plugin Zip File": "プラグインZipファイル", "Compressed file url": "圧縮ファイルのURL", "Last updated": "最終更新", "PackageName": "パッケージ名", "DisplayName": "表示名", "Readme": "<PERSON><PERSON>", "Dependencies compatibility check": "依存関係の互換性チェック", "If the compatibility check fails, you should change the dependent version to meet the version requirements.": "互換性チェックに失敗した場合は、依存関係のバージョンを変更して、バージョン要件を満たす必要があります。", "Version range": "バージョン範囲", "Plugin's version": "プラグインのバージョン", "Result": "結果", "No CHANGELOG.md file": "CHANGELOG.mdファイルがありません", "No README.md file": "README.mdファイルがありません", "Homepage": "ホームページ", "Drag and drop the file here or click to upload, file size should not exceed 30M": "ファイルをここにドラッグ＆ドロップするか、クリックしてアップロードしてください。ファイルサイズは10Mを超えてはいけません", "Dependencies check failed, can't enable.": "依存関係のチェックに失敗しました。有効にできません。", "Plugin starting...": "プラグインを起動しています...", "Plugin stopping...": "プラグインを停止しています...", "Are you sure to delete this plugin?": "このプラグインを削除してもよろしいですか？", "Are you sure to delete this plugin": "本当にこのプラグインを無効にしますか？", "re-download file": "ファイルを再ダウンロード", "Not enabled": "有効になっていません", "Search plugin": "プラグインを検索", "Author": "著者", "Plugin loading failed. Please check the server logs.": "プラグインのロードに失敗しました。サーバーログを確認してください。", "Automatically drop objects that depend on the collection (such as views), and in turn all objects that depend on those objects": "テーブルに依存するオブジェクト、およびそれらに依存するオブジェクトを自動的に削除する", "Allow add new": "新規作成を許可", "Allow selection of existing records": "既存のデータの選択を許可", "loading": "ロード中", "name is required": "名前は必須です", "data source": "データソース", "Data source": "データソース", "DataSource": "データソース", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "{{type}} \"{{name}}\" は削除されている可能性があります。この {{blockType}} を削除してください。", "Home page": "ホームページ", "Handbook": "ユーザーマニュアル", "License": "ライセンス", "This variable has been deprecated and can be replaced with \"Current form\"": "この変数は非推奨です。代わりに「現在のフォーム」を使用してください", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "この変数の値はページURLのクエリ文字列から取得されます。この変数は、ページにクエリ文字列がある場合にのみ正常に使用できます。", "URL search params": "URL検索パラメータ", "Expand All": "すべて展開", "Clear default value": "デフォルト値をクリア", "Open in new window": "新しいウィンドウで開く", "Sorry, the page you visited does not exist.": "申し訳ありませんが、お探しのページは存在しません。", "Ellipsis overflow content": "省略記号で内容を省略", "NaN": "なし", "Settings": "設定", "Collection selector": "コレクションセレクタ", "Providing certain collections as options for users, typically used in polymorphic or inheritance scenarios": "ユーザーに提供する特定のコレクションは多態性や継承シナリオで使用されます。", "Problematic": "問題あり", "Setting": "設定", "List": "リスト", "Grid Card": "グリッドカード", "pixels": "ピクセル", "Screen size": "スクリーンサイズ", "Display title": "タイトルを表示", "Set the count of columns displayed in a row": "列の数を設定", "Column": "カラム", "Phone device": "携帯デバイス", "Tablet device": "タブレットデバイス", "Desktop device": "デスクトップデバイス", "Large screen device": "大画面デバイス", "Delete events": "イベントを削除", "This event": "このイベント", "This and following events": "このイベントおよび後続のイベント", "All events": "すべてのイベント", "Delete this event?": "このイベントを削除しますか？", "Delete Event": "イベントを削除", "Default collapse": "デフォルト閉じる", "Insert if not exists": "存在しない場合に挿入", "Insert if not exists, or update": "存在しない場合に挿入、さもなくば更新", "Determine whether a record exists by the following fields": "次のフィールドでレコードが存在するかどうかを判断", "Update record": "レコードを更新", "Primary key, unique identifier, self growth": "主キー、ユニークID、自動増加", "Store the creation user of each record": "各レコードの作成者を保存", "Store the last update user of each record": "各レコードの最終更新者を保存", "Store the creation time of each record": "各レコードの作成日時を保存", "Store the last update time of each record": "各レコードの最終更新日時を保存", "More options": "その他のオプション", "Sync from database": "データベースから同期", "Association fields filter": "関連フィールドフィルター", "PK & FK fields": "PK & FKフィールド", "Association fields": "関連フィールド", "System fields": "システムフィールド", "General fields": "一般フィールド", "Inherited fields": "継承フィールド", "Parent collection fields": "親コレクションフィールド", "Inverse relationship type": "逆関連付けタイプ", "Generated automatically if left blank": "空欄のままにすると自動的に生成されます", "Display field title": "フィールドタイトルを表示", "Toggles the subfield mode": "サブフィールドモードを切り替える", "Selector mode": "セレクターモード", "Subtable mode": "サブテーブルモード", "Subform mode": "サブフォームモード", "Pattern": "パターン", "Operator": "演算子", "Import": "インポート", "Custom": "カスタム", "Primary": "主キー", "Auto increment": "自動インクリメント", "Max length": "最大長さ", "Min length": "最小長さ", "Length": "長さ", "The field value cannot be greater than ": "数値は...より大きくてはならない", "The field value cannot be less than ": "数値は...より小さくてはならない", "The field value is not an integer number": "数字は整数ではありません", "Background Color": "背景色", "Allow uploading multiple files": "複数のファイルのアップロードを許可する", "Custom title": "カスタムタイトル", "Daily": "毎日", "Weekly": "毎週", "Monthly": "毎月", "Yearly": "毎年", "Repeats": "繰り返し", "Show lunar": "旧暦を表示", "Importable fields": "インポート可能なフィールド", "Sign in via account": "アカウントでサインイン", "Sign in via phone": "電話番号でサインイン", "Verification code": "認証コード", "Send code": "認証コードを送信", "Retry after {{count}} seconds": "{{count}} 秒後に再試行", "Popup size": "ポップアップサイズ", "Small": "スモール", "Middle": "ミドル", "Large": "ラージ", "Size": "サイズ", "Oversized": "特大", "Auto": "自動", "Object Fit": "オブジェクトフィット", "Cover": "カバー", "Fill": "フィル", "Contain": "コンテイン", "Scale Down": "スケールダウン", "Identifier for program usage. Support letters, numbers and underscores, must start with an letter.": "プログラム使用のための識別子。文字、数字、...", "Style": "スタイル", "Allows to configure interface": "インターフェースの設定を許可", "Allows to install, activate, disable plugins": "プラグインのインストール、アクティブ化、無効化を許可", "Allows to configure plugins": "プラグインの設定を許可", "Parent record": "親レコード", "Current time": "現在の時刻", "System variables": "システム変数", "Date variables": "日付変数", "Select": "選択", "Done": "完了", "File manager": "ファイルマネージャー", "ACL": "アクセス制御", "Collection manager": "コレクション管理", "Plugin manager": "プラグイン管理", "Local": "ローカル", "Built-in": "内蔵", "Marketplace": "マーケットプレイス", "Add plugin": "プラグインを追加", "Plugin source": "プラグインソース", "Plugin dependencies check failed": "プラグインの依存関係のチェックに失敗しました", "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.": "プラグインの依存関係チェックに失敗しました。依存バージョンを変更して要件を満たしてください。", "Are you sure to disable this plugin?": "このプラグインを無効にしてもよろしいですか？", "Coming soon...": "近日公開予定です...", "All plugin settings": "すべてのプラグイン設定", "Bookmark": "ブックマーク", "Manage all settings": "すべての設定を管理", "Create inverse field in the target collection": "ターゲットコレクションに逆フィールドを作成", "Inverse field name": "逆フィールド名", "Inverse field display name": "逆フィールド表示名", "Bulk update": "一括更新", "After successful bulk update": "一括更新が成功した後", "Bulk edit": "一括編集", "Data will be updated": "データが更新されます", "Selected": "選択済み", "All": "すべて", "Update selected data?": "選択したデータを更新しますか？", "Update all data?": "すべてのデータを更新しますか？", "Remains the same": "変更なし", "Changed to": "に変更", "Clear": "クリア", "Add attach": "添付を追加", "Please select the records to be updated": "更新するレコードを選択してください", "Selector": "セレクタ", "Inner": "内部", "Please fill in the iframe URL": "iframe URLを入力してください", "Fix block": "ブロックを固定", "Plugin name": "プラグイン名", "Plugin tab name": "プラグインタブ名", "Column width": "列幅", "Sortable": "ソート可能", "Enable link": "リンクを有効にする", "App error": "アプリエラー", "Download logs": "ログをダウンロード", "Assign data scope for the template": "テンプレートのデータ範囲を指定", "Table selected records": "選択されたレコード", "Restart": "再起動", "Restart application": "アプリケーションを再起動", "Execute": "実行", "Please use a valid SELECT or WITH AS statement": "有効な SELECT または WITH AS 文を使用してください", "Please confirm the SQL statement first": "SQL 文を確認してください", "Sign in with another account": "他のアカウントでサインイン", "Return to the main application": "メインアプリケーションに戻る", "Permission deined": "アクセス拒否", "Preset fields": "プリセットフィールド", "Generic properties": "汎用プロパティ", "Specific properties": "特定のプロパティ", "Used for drag and drop sorting scenarios, supporting grouping sorting": "ドラッグ＆ドロップによる並べ替えで使用され、グループ分けソートをサポート", "Grouped sorting": "グループ分けソート", "When a field is selected for grouping, it will be grouped first before sorting.": "フィールドをグループに選択すると、最初にグループ化され、その後ソートされます", "Departments": "部門", "Main department": "主要部門", "Department name": "部門名", "Superior department": "上位部門", "Owners": "責任者", "Plugin settings": "プラグイン設定", "Menu": "メニュー", "Drag and drop sorting field": "ドラッグ＆ドロップソートフィールド", "Search": "検索", "is none of": "どれにも一致しない", "is any of": "いずれかに一致する", "Plugin dependency version mismatch": "プラグインの依存バージョンが一致しません", "The current dependency version of the plugin does not match the version of the application and may not work properly. Are you sure you want to continue enabling the plugin?": "現在のプラグインの依存バージョンがアプリのバージョンと異なるため、正常に動作しない可能性があります。それでもプラグインを有効化しますか？", "Page number": "ページ番号", "Page size": "ページサイズ", "Enable": "有効化", "Disable": "無効化", "Calculation engine": "計算エンジン", "Expression collection": "式コレクション", "Tree collection": "ツリーコレクション", "Parent ID": "親ID", "Parent": "親", "Children": "子", "Confirm": "確認", "Block": "ブロック", "Unnamed": "無名", "SQL collection": "SQLコレクション", "Configure field": "フィールドを設定", "Username": "ユーザー名", "Null": "ヌル", "Boolean": "ブール値", "String": "文字列", "Syntax references": "構文リファレンス", "Math.js comes with a large set of built-in functions and constants, and offers an integrated solution to work with different data types.": "Math.jsには多くの組み込み関数と定数があり、異なるデータ型を扱うための統合ソリューションを提供します。", "Formula.js supports most Microsoft Excel formula functions.": "Formula.jsはMicrosoft Excelの大半の関数をサポートしています。", "String template": "文字列テンプレート", "Simple string replacement, can be used to interpolate variables in a string.": "シンプルな文字列置換で、文字列に変数を埋め込むことができます。", "Display <icon></icon> when unchecked": "未チェック時に <icon></icon> を表示", "Allow dissociate": "関連解除を許可", "Must be 1-50 characters in length (excluding @.<>\"'/)": "1〜50文字で、@.<>\"'/は使用できません", "Data source permissions": "データソースの権限", "Now": "現在", "Access control": "アクセス制御", "Remove": "削除", "Docs": "ドキュメント", "Enable page header": "ページヘッダーを有効にする", "Display page title": "ページタイトルを表示", "Edit page title": "ページタイトルを編集", "Enable page tabs": "ページタブを有効にする", "Constant": "定数", "Select a variable": "変数を選択", "Double click to choose entire object": "ダブルクリックして全体オブジェクトを選択", "true": "真", "false": "偽", "Prettify": "整形", "Theme": "テーマ", "Default theme": "デフォルトテーマ", "Compact theme": "コンパクトテーマ", "Download": "ダウンロード", "Support for a single or bulk upload.": "単一または一括アップロードに対応します。", "File size should not exceed {{size}}.": "ファイルサイズは{{size}}を超えてはいけません。", "File size exceeds the limit": "ファイルサイズが制限を超えています", "File type is not allowed": "ファイルタイプが許可されていません", "Incomplete uploading files need to be resolved": "未完了のアップロードファイルを処理する必要があります", "Enable form data template": "フォームデータテンプレートを有効にする", "No configuration available.": "設定可能な項目がありません。", "Reload application": "アプリケーションを再読み込み", "The application is reloading, please do not close the page.": "アプリケーションが再読み込み中です。ページを閉じないでください。", "Application reloading": "アプリケーションを再読み込み中", "Allows to clear cache, reboot application": "キャッシュのクリアとアプリケーションの再起動が可能", "The will interrupt service, it may take a few seconds to restart. Are you sure to continue?": "再起動はサービスを中断します。再起動には数秒かかる場合があります。続行しますか？", "Clear cache": "キャッシュをクリア", "Quick create": "クイック作成", "Dropdown": "ドロップダウン", "Pop-up": "ポップアップ", "Direct duplicate": "直接複製", "Copy into the form and continue to fill in": "フォームにコピーして入力を続ける", "Failed to load plugin": "プラグインの読み込みに失敗しました", "Filter out a single piece or a group of records as a template": "単一または複数のレコードをテンプレートとしてフィルタリング", "The title field is used to identify the template record": "タイトルフィールドはテンプレートレコードを識別するために使用されます", "Template fields": "テンプレートフィールド", "The selected fields will automatically populate the form": "選択されたフィールドがフォームに自動入力されます", "UnSelect all": "全ての選択を解除", "Secondary confirmation": "二次確認", "Perform the {{title}}": "{{title}}を実行", "Are you sure you want to perform the {{title}} action?": "{{title}}操作を実行してもよろしいですか？", "Permission denied": "権限が拒否されました", "Data model": "データモデル", "Security": "セキュリティ", "Action": "アクション", "System": "システム", "Other": "その他", "Data Model": "データモデル", "Blocks": "ブロック", "Users & permissions": "ユーザーと権限", "System management": "システム管理", "System & security": "システムとセキュリティ", "Workflow": "ワークフロー", "Third party services": "サードパーティサービス", "Data model tools": "データモデルツール", "Data sources": "データソース", "Collections": "コレクション", "Collection fields": "コレクションフィールド", "Authentication": "認証", "Logging and monitoring": "ログおよび監視", "Main": "メイン", "Index": "インデックス", "Field values must be unique.": "フィールド値は一意でなければなりません", "Alphabet": "アルファベット", "Accuracy": "精度", "Millisecond": "ミリ秒", "Second": "秒", "Unix Timestamp": "Unixタイムスタンプ", "Field value do not meet the requirements": "フィールド値が要件を満たしていません", "Field value size is": "フィールド値のサイズは", "Unit conversion": "単位変換", "Separator": "区切り", "Prefix": "接頭辞", "Suffix": "接尾辞", "Record unique key": "レコードのユニークキー", "Filter target key": "フィルター対象キー", "If a collection lacks a primary key, you must configure a unique record key to locate row records within a block, failure to configure this will prevent the creation of data blocks for the collection.": "コレクションにプライマリキーがない場合、行レコードを特定するためのユニークキーを設定する必要があります。設定しないと、コレクションのデータブロックが作成できません。", "Filter data based on the specific field, with the requirement that the field value must be unique.": "特定フィールドに基づいてデータをフィルタリングしますが、そのフィールド値は一意である必要があります。", "Multiply by": "掛ける", "Divide by": "割る", "Scientifix notation": "科学的表記", "Normal": "通常", "Automatically generate default values": "自動でデフォルト値を生成", "Refresh data on close": "閉じるとデータを更新", "Refresh data on action": "アクション時にデータを更新", "Unknown field type": "不明なフィールドタイプ", "The following field types are not compatible and do not support output and display": "次のフィールドタイプは互換性がなく、出力および表示をサポートしていません", "Not fixed": "固定されていない", "Left fixed": "左に固定", "Right fixed": "右に固定", "Fixed": "固定", "Set block height": "ブロックの高さを設定", "Specify height": "高さを指定", "Full height": "全画面表示", "Please configure the URL": "URLを設定してください", "URL": "URL", "Search parameters": "検索パラメーター", "Do not concatenate search params in the URL": "URLに検索パラメーターを連結しない", "Edit link": "リンクを編集", "Add parameter": "パラメーターを追加", "Use simple pagination mode": "シンプルなページネーションモードを使用", "Set Template Engine": "テンプレートエンジンを設定", "Skip getting the total number of table records during paging to speed up loading. It is recommended to enable this option for data tables with a large amount of data": "ページング時にテーブルレコードの総数取得をスキップして、読み込み速度を向上させます。データ量が多い場合にこのオプションの使用をお勧めします。", "The current user only has the UI configuration permission, but don't have view permission for collection \"{{name}}\"": "現在のユーザーにはUI設定の権限しかなく、コレクション「{{name}}」を閲覧する権限はありません。", "Allow multiple selection": "複数選択を許可", "Parent object": "親オブジェクト", "Hide column": "列を非表示", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "設定モードでは、列全体が透明になります。非設定モードでは、列全体が非表示になります。列全体が非表示になっても、設定されたデフォルト値やその他の設定は依然として有効です。", "Desktop routes": "デスクトップルート", "Route permissions": "ルートの権限", "New routes are allowed to be accessed by default": "新しいルートはデフォルトでアクセス可能", "Route name": "ルート名", "Mobile routes": "モバイルルート", "Show in menu": "メニューに表示", "Hide in menu": "メニューに非表示", "Path": "パス", "Type": "タイプ", "Access": "アクセス", "Routes": "ルート", "Add child route": "子ルートを追加", "Delete routes": "ルートを削除", "Delete route": "ルートを削除", "Are you sure you want to hide these routes in menu?": "これらのルートをメニューに非表示にしますか？", "Are you sure you want to show these routes in menu?": "これらのルートをメニューに表示しますか？", "Are you sure you want to hide this menu?": "このメニューを非表示にしますか？", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "非表示にすると、このメニューはメニューバーに表示されなくなります。再表示するには、ルート管理ページで設定する必要があります。", "If selected, the page will display Tab pages.": "選択されている場合、ページはタブページを表示します。", "If selected, the route will be displayed in the menu.": "選択されている場合、ルートはメニューに表示されます。", "Are you sure you want to hide this tab?": "このタブを非表示にしますか？", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "非表示にすると、このタブはタブバーに表示されなくなります。再表示するには、ルート管理ページで設定する必要があります。", "No pages yet, please configure first": "まだページがありません。最初に設定してください", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "ユーザーインターフェースエディターモードに入るには、右上隅の「UIエディタ」アイコンをクリックしてください", "Deprecated": "非推奨", "Full permissions": "すべての権限", "Refresh data blocks": "データブロックを更新", "Select data blocks to refresh": "データブロックを選択して更新", "After successful submission, the selected data blocks will be automatically refreshed.": "送信後、選択したデータブロックが自動的に更新されます。", "Reset link expiration": "リンクの有効期限をリセット"}