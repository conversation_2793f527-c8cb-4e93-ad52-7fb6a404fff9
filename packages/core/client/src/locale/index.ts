/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

export type LocaleOptions = {
  label: string;
};

export const dayjsLocale = {
  'ar-EG': 'ar',
  'az-AZ': 'az',
  'bg-BG': 'bg',
  'bn-BD': 'bn-bd',
  'by-BY': 'en',
  'ca-ES': 'ca',
  'cs-CZ': 'cs',
  'da-DK': 'da',
  'de-DE': 'de',
  'el-GR': 'el',
  'en-GB': 'en-gb',
  'en-US': 'en',
  'es-ES': 'es',
  'et-EE': 'et',
  'fa-IR': 'fa',
  'fi-FI': 'fi',
  'fr-BE': 'fr',
  'fr-CA': 'fr',
  'fr-FR': 'fr',
  'ga-IE': 'ga',
  'gl-ES': 'gl',
  'he-IL': 'he',
  'hi-IN': 'hi',
  'hr-HR': 'hr',
  'hu-HU': 'hu',
  'hy-AM': 'hy-am',
  'id-ID': 'id',
  'is-IS': 'is',
  'it-IT': 'it',
  'ja-JP': 'ja',
  'ka-GE': 'ka',
  'kk-KZ': 'kk',
  'km-KH': 'km',
  // 'kmr-IQ': { label: 'kmr_IQ' },
  'kn-IN': 'kn',
  'ko-KR': 'ko',
  'ku-IQ': 'ku',
  'lt-LT': 'lt',
  'lv-LV': 'lv',
  'mk-MK': 'mk',
  'ml-IN': 'ml',
  'mn-MN': 'mn',
  'ms-MY': 'ms',
  'nb-NO': 'nb',
  'ne-NP': 'ne',
  'nl-BE': 'nl-be',
  'nl-NL': 'nl',
  'pl-PL': 'pl',
  'pt-BR': 'pt-br',
  'pt-PT': 'pt',
  'ro-RO': 'ro',
  'ru-RU': 'ru',
  'si-LK': 'si',
  'sk-SK': 'sk',
  'sl-SI': 'sl',
  'sr-RS': 'sr',
  'sv-SE': 'sv',
  'ta-IN': 'ta',
  'th-TH': 'th',
  'tk-TK': 'tk',
  'tr-TR': 'tr',
  'uk-UA': 'uk',
  'ur-PK': 'ur',
  'vi-VN': 'vi',
  'zh-CN': 'zh-cn',
  'zh-HK': 'zh-hk',
  'zh-TW': 'zh-tw',
};

export const languageCodes = {
  'ar-EG': { label: 'العربية' },
  'az-AZ': { label: 'Azərbaycan dili' },
  'bg-BG': { label: 'Български' },
  'bn-BD': { label: 'Bengali' },
  'by-BY': { label: 'Беларускі' },
  'ca-ES': { label: 'Сatalà/Espanya' },
  'cs-CZ': { label: 'Česky' },
  'da-DK': { label: 'Dansk' },
  'de-DE': { label: 'Deutsch' },
  'el-GR': { label: 'Ελληνικά' },
  'en-GB': { label: 'English(GB)' },
  'en-US': { label: 'English' },
  'es-ES': { label: 'Español' },
  'et-EE': { label: 'Estonian (Eesti)' },
  'fa-IR': { label: 'فارسی' },
  'fi-FI': { label: 'Suomi' },
  'fr-BE': { label: 'Français(BE)' },
  'fr-CA': { label: 'Français(CA)' },
  'fr-FR': { label: 'Français' },
  'ga-IE': { label: 'Gaeilge' },
  'gl-ES': { label: 'Galego' },
  'he-IL': { label: 'עברית' },
  'hi-IN': { label: 'हिन्दी' },
  'hr-HR': { label: 'Hrvatski jezik' },
  'hu-HU': { label: 'Magyar' },
  'hy-AM': { label: 'Հայերեն' },
  'id-ID': { label: 'Bahasa Indonesia' },
  'is-IS': { label: 'Íslenska' },
  'it-IT': { label: 'Italiano' },
  'ja-JP': { label: '日本語' },
  'ka-GE': { label: 'ქართული' },
  'kk-KZ': { label: 'Қазақ тілі' },
  'km-KH': { label: 'ភាសាខ្មែរ' },
  // 'kmr-IQ': { label: 'kmr_IQ' },
  'kn-IN': { label: 'ಕನ್ನಡ' },
  'ko-KR': { label: '한국어' },
  'ku-IQ': { label: 'کوردی' },
  'lt-LT': { label: 'lietuvių' },
  'lv-LV': { label: 'Latviešu valoda' },
  'mk-MK': { label: 'македонски јазик' },
  'ml-IN': { label: 'മലയാളം' },
  'mn-MN': { label: 'Монгол хэл' },
  'ms-MY': { label: 'بهاس ملايو' },
  'nb-NO': { label: 'Norsk bokmål' },
  'ne-NP': { label: 'नेपाली' },
  'nl-BE': { label: 'Vlaams' },
  'nl-NL': { label: 'Nederlands' },
  'pl-PL': { label: 'Polski' },
  'pt-BR': { label: 'Português brasileiro' },
  'pt-PT': { label: 'Português' },
  'ro-RO': { label: 'România' },
  'ru-RU': { label: 'Русский' },
  'si-LK': { label: 'සිංහල' },
  'sk-SK': { label: 'Slovenčina' },
  'sl-SI': { label: 'Slovenščina' },
  'sr-RS': { label: 'српски језик' },
  'sv-SE': { label: 'Svenska' },
  'ta-IN': { label: 'Tamil' },
  'th-TH': { label: 'ภาษาไทย' },
  'tk-TK': { label: 'Turkmen' },
  'tr-TR': { label: 'Türkçe' },
  'uk-UA': { label: 'Українська' },
  'ur-PK': { label: 'Oʻzbekcha' },
  'vi-VN': { label: 'Tiếng Việt' },
  'zh-CN': { label: '简体中文' },
  'zh-HK': { label: '繁體中文（香港）' },
  'zh-TW': { label: '繁體中文（台湾）' },
};

export default languageCodes;
