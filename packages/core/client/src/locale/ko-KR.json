{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "페이지당 표시 <1><0>10</0><1>20</1><2>50</2><3>100</3></1> 개 항목", "Page number": "페이지 번호", "Page size": "페이지 크기", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "그룹 내 조건 <1><0>모두</0><1>어떤</1></1> 충족", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "<1><0>모달</0><1>서랍</1><2>창</2></1>에서 열기", "{{count}} filter items": "{{count}}개 필터 항목", "{{count}} more items": "더 보기 {{count}} 개 항목", "Total {{count}} items": "총 {{count}} 개 항목", "Today": "오늘", "Yesterday": "어제", "Tomorrow": "내일", "Month": "월", "Week": "주", "This week": "이번 주", "Next week": "다음 주", "This month": "이번 달", "Next month": "다음 달", "Last quarter": "지난 분기", "This quarter": "이번 분기", "Next quarter": "다음 분기", "This year": "올해", "Next year": "내년", "Last week": "지난 주", "Last month": "지난 달", "Last year": "작년", "Last 7 days": "지난 7일", "Last 30 days": "지난 30일", "Last 90 days": "지난 90일", "Next 7 days": "다음 7일", "Next 30 days": "다음 30일", "Next 90 days": "다음 90일", "Work week": "근무 주", "Day": "일", "Agenda": "일정", "Date": "날짜", "Time": "시간", "Event": "이벤트", "None": "없음", "Unconnected": "연결되지 않음", "System settings": "시스템 설정", "System title": "시스템 제목", "Setting": "설정", "Settings": "설정", "Enable": "활성화", "Disable": "비활성화", "On": "켜기", "Off": "끄기", "Logo": "로고", "Add menu item": "메뉴 항목 추가", "Page": "페이지", "Tab": "탭", "Name": "이름", "Icon": "아이콘", "Group": "그룹", "Link": "링크", "Save conditions": "조건 저장", "Edit menu item": "메뉴 항목 편집", "Move to": "이동", "Insert left": "왼쪽에 삽입", "Insert right": "오른쪽에 삽입", "Insert inner": "내부에 삽입", "Delete": "삭제", "Disassociate": "연결 해제", "Disassociate record": "레코드 연결 해제", "Are you sure you want to disassociate it?": "정말로 연결을 해제하시겠습니까?", "UI editor": "UI 편집기", "Collection": "컬렉션", "Collection selector": "컬렉션 선택기", "Providing certain collections as options for users, typically used in polymorphic or inheritance scenarios": "특정 컬렉션을 사용자 옵션으로 제공하여 일반적으로 다형성 또는 상속 시나리오에서 사용됩니다", "Enable child collections": "하위 컬렉션 활성화", "Allow adding records to the current collection": "현재 컬렉션에 레코드 추가 허용", "Collections & Fields": "컬렉션 및 필드", "All collections": "모든 컬렉션", "Add category": "카테고리 추가", "Delete category": "카테고리 삭제", "Edit category": "카테고리 편집", "Collection category": "컬렉션 카테고리", "Collection template": "컬렉션 템플릿", "Visible": "가시성", "Read only": "읽기 전용", "Easy reading": "가독성 향상", "Hidden": "숨김", "Hidden(reserved value)": "숨김(예약 값)", "Not required": "필수 아님", "Value": "값", "Disabled": "비활성화", "Enabled": "활성화", "Problematic": "문제 발생", "Empty": "비어있음", "Linkage rule": "연동 규칙", "Linkage rules": "연동 규칙", "Condition": "조건", "Properties": "속성", "Add linkage rule": "연동 규칙 추가", "Add property": "속성 추가", "Calculation engine": "계산 엔진", "Expression": "표현식", "Expression collection": "표현식 컬렉션", "Sort": "정렬", "Categories": "카테고리", "Category name": "카테고리 이름", "Add child": "하위 항목 추가", "Collapse all": "모두 축소", "Expand all": "모두 확장", "Expand/Collapse": "확장/축소", "Default collapse": "기본 축소", "Tree collection": "트리 컬렉션", "Tree table": "트리 테이블", "Parent ID": "상위 레코드 ID", "Parent": "상위 레코드", "Children": "하위 레코드", "Roles & Permissions": "역할 및 권한", "Edit profile": "프로필 편집", "Change password": "비밀번호 변경", "Old password": "이전 비밀번호", "New password": "새 비밀번호", "Switch role": "역할 전환", "Super admin": "최고 관리자", "Language": "언어", "Allow sign up": "가입 허용", "Enable SMS authentication": "SMS 인증 활성화", "Sign out": "로그아웃", "Cancel": "취소", "Confirm": "확인", "Submit": "제출", "Close": "닫기", "Set the data scope": "데이터 범위 설정", "Data loading mode": "데이터 로드 모드", "Set data loading mode": "데이터 로드 모드 설정", "Load all data when filter is empty": "필터가 비어 있을 때 모든 데이터 로드", "Do not load data when filter is empty": "필터가 비어 있을 때 데이터 로드 안 함", "Block": "블록", "Data blocks": "데이터 블록", "Filter blocks": "필터 블록", "Table": "테이블", "Form": "폼", "List": "목록", "Grid Card": "그리드 카드", "Screen size": "화면 크기", "pixels": "픽셀", "Display title": "표시 제목", "Set the count of columns displayed in a row": "한 행에 표시되는 열 수 설정", "Column": "열", "Phone device": "휴대폰 기기", "Tablet device": "태블릿 기기", "Desktop device": "데스크톱 기기", "Large screen device": "대형 화면 기기", "Table OID(Inheritance)": "테이블 OID(상속)", "Collapse": "축소", "Select data source": "데이터 소스 선택", "Calendar": "캘린더", "Delete events": "일정 삭제", "This event": "이 이벤트", "This and following events": "이 이벤트 및 이후 이벤트", "All events": "모든 이벤트", "Delete this event?": "이 이벤트를 삭제하시겠습니까?", "Delete Event": "이벤트 삭제", "Kanban": "칸반", "Gantt": "간트 차트", "Create gantt block": "간트 블록 생성", "Progress field": "진행 상태 필드", "Time scale": "시간 축척", "Hour": "시간", "Quarter of day": "하루의 1/4", "Half of day": "하루의 1/2", "Year": "년", "QuarterYear": "분기", "Select grouping field": "그룹화 필드 선택", "Media": "미디어", "Markdown": "마크다운", "Wysiwyg": "위지위그", "Chart blocks": "차트 블록", "Column chart": "막대 차트", "Bar chart": "막대 차트", "Line chart": "선형 차트", "Pie chart": "파이 차트", "Area chart": "영역 차트", "Other chart": "기타 차트", "Other blocks": "기타 블록", "In configuration": "구성 중", "Chart title": "차트 제목", "Chart type": "차트 유형", "Chart config": "차트 구성", "Templates": "템플릿", "Template": "템플릿", "Select template": "템플릿 선택", "Action logs": "작업 로그", "Create template": "템플릿 생성", "Edit markdown": "마크다운 편집", "Add block": "블록 추가", "Add new": "새로 추가", "Add record": "레코드 추가", "Custom field display name": "사용자 정의 필드 표시 이름", "Display fields": "표시 필드", "Edit record": "레코드 편집", "Delete menu item": "메뉴 항목 삭제", "Add page": "페이지 추가", "Add group": "그룹 추가", "Add link": "링크 추가", "Insert above": "위에 삽입", "Insert below": "아래에 삽입", "Save": "저장", "Delete block": "블록 삭제", "Are you sure you want to delete it?": "정말로 삭제하시겠습니까?", "This is a demo text, **supports Markdown syntax**.": "이는 데모 텍스트이며, **Markdown 구문을 지원**합니다.", "Filter": "필터", "Connect data blocks": "데이터 블록 연결", "Action type": "액션 유형", "Actions": "액션", "Update": "업데이트", "Update record": "레코드 업데이트", "Unnamed": "이름 없음", "View": "보기", "View record": "레코드 보기", "Refresh": "새로고침", "Data changes": "데이터 변경", "Field name": "필드 이름", "Before change": "변경 전", "After change": "변경 후", "Delete record": "레코드 삭제", "Create collection": "데이터 테이블 생성", "Collection display name": "데이터 테이블 표시 이름", "Collection name": "데이터 테이블 이름", "Inherits": "상속", "Primary key, unique identifier, self growth": "기본 키, 고유 ID, 증가", "Store the creation user of each record": "각 레코드의 생성 사용자 저장", "Store the last update user of each record": "각 레코드의 마지막 업데이트 사용자 저장", "Store the creation time of each record": "각 레코드의 생성 시간 저장", "Store the last update time of each record": "각 레코드의 마지막 업데이트 시간 저장", "More options": "더 많은 옵션", "Records can be sorted": "레코드를 정렬할 수 있음", "Calendar collection": "캘린더 데이터 테이블", "General collection": "일반 데이터 테이블", "SQL collection": "SQL 데이터 테이블", "Connect to database view": "데이터베이스 뷰에 연결", "Sync from database": "데이터베이스에서 동기화", "Source collections": "소스 데이터 테이블", "Field source": "필드 소스", "Preview": "미리보기", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with a letter.": "임의로 생성되며 수정할 수 있습니다. 영문자, 숫자 및 밑줄을 지원하며, 영문자로 시작해야 합니다.", "Edit": "편집", "Edit collection": "데이터 테이블 편집", "Configure field": "필드 구성", "Configure fields": "필드 구성", "Configure columns": "열 구성", "Edit field": "필드 편집", "Override": "재정의", "Override field": "필드 재정의", "Configure fields of {{title}}": "{{title}}의 필드 구성", "Association fields filter": "관련 필드 필터", "PK & FK fields": "기본키 및 외래키 필드", "Association fields": "관련 필드", "Choices fields": "선택 필드", "System fields": "시스템 필드", "General fields": "일반 필드", "Inherited fields": "상속된 필드", "Parent collection fields": "상위 데이터 테이블 필드", "Basic": "기본 유형", "Single line text": "한 줄 텍스트", "Long text": "긴 텍스트", "Phone": "전화번호", "Email": "이메일", "Username": "사용자 이름", "Null": "널", "Boolean": "부울", "Number": "숫자", "Integer": "정수", "Percent": "퍼센트", "String": "문자열", "Password": "비밀번호", "Advanced type": "고급 유형", "Formula": "수식", "Formula description": "동일한 레코드 내의 다른 필드를 기반으로 값을 계산하는 데 사용됩니다.", "Syntax references": "구문 참조", "Math.js comes with a large set of built-in functions and constants, and offers an integrated solution to work with different data types": "Math.js는 다양한 내장 함수 및 상수를 제공하며 여러 데이터 유형과 작업하는 통합 솔루션을 제공합니다.", "Formula.js supports most Microsoft Excel formula functions.": "Formula.js는 대부분의 Microsoft Excel 수식 함수를 지원합니다.", "Choices": "선택 유형", "Checkbox": "체크박스", "Display <icon></icon> when unchecked": "체크되지 않았을 때 <icon></icon> 표시", "Single select": "단일 선택", "Multiple select": "다중 선택", "Radio group": "라디오 그룹", "Checkbox group": "체크박스 그룹", "China region": "중국 지역", "Date & Time": "날짜 및 시간", "Datetime": "날짜", "Relation": "관계 유형", "Link to": "링크", "Link to description": "빠른 데이터 테이블 관계 생성에 사용되며 대부분의 표준 시나리오와 호환됩니다. 비개발자에게 적합합니다. 필드로 존재할 때, 목적 데이터 테이블의 데이터를 선택할 수 있는 드롭다운이 됩니다. 생성 후, 현재 데이터 테이블의 관련 필드가 목적 데이터 테이블에 동시에 생성됩니다.", "Sub-table": "하위 테이블", "System info": "시스템 정보", "Created at": "생성 날짜", "Last updated at": "최근 수정 날짜", "Created by": "생성자", "Last updated by": "최근 수정자", "Add field": "필드 추가", "Field display name": "필드 표시 이름", "Field type": "필드 유형", "Field interface": "필드 인터페이스", "Date format": "날짜 형식", "Year/Month/Day": "년/월/일", "Year-Month-Day": "년-월-일", "Day/Month/Year": "일/월/년", "Show time": "시간 표시", "Time format": "시간 형식", "12 hour": "12 시간", "24 hour": "24 시간", "Relationship type": "관계 유형", "Inverse relationship type": "역 관계 유형", "Source collection": "소스 데이터 테이블", "Source key": "소스 데이터 테이블 필드 식별자", "Target collection": "대상 데이터 테이블", "Through collection": "중간 데이터 테이블", "Target key": "대상 데이터 테이블 필드 식별자", "Foreign key": "외래키", "One to one": "일 대 일", "One to many": "일 대 다", "Many to one": "다 대 일", "Many to many": "다 대 다", "Foreign key 1": "외래키 1", "Foreign key 2": "외래키 2", "One to one description": "일 대 일 관계를 만들기 위해 사용되며, 예를 들어 사용자당 하나의 프로필이 있을 수 있습니다.", "One to many description": "일 대 다 관계를 만들기 위해 사용되며, 예를 들어 한 국가당 여러 도시가 있을 수 있습니다. 필드로 존재할 때, 대상 데이터 테이블의 데이터를 표시하는 하위 테이블입니다. 생성된 후, 대상 데이터 테이블에는 자동으로 일 대 다 필드가 생성됩니다.", "Many to one description": "다 대 일 관계를 만들기 위해 사용되며, 예를 들어 도시 하나는 하나의 국가에 속하며, 국가 하나는 여러 도시를 가질 수 있습니다. 필드로 존재할 때, 대상 데이터 테이블의 데이터를 선택할 수 있는 드롭다운입니다. 생성된 후, 대상 데이터 테이블에는 자동으로 다 대 일 필드가 생성됩니다.", "Many to many description": "다 대 다 관계를 만들기 위해 사용되며, 예를 들어 한 학생은 여러 선생님을 가질 수 있으며, 한 선생님도 여러 학생을 가질 수 있습니다. 필드로 존재할 때, 대상 데이터 테이블의 데이터를 선택할 수 있는 드롭다운입니다.", "Generated automatically if left blank": "비워 두면 자동 생성됩니다", "Display association fields": "관련 테이블 필드 표시", "Display field title": "필드 제목 표시", "Field component": "필드 컴포넌트", "Allow multiple": "여러 항목 추가/관련 허용", "Allow dissociate": "이미 관련된 레코드 제거 허용", "Quick upload": "빠른 업로드", "Select file": "파일 선택", "Subtable": "하위 테이블", "Sub-form": "하위 폼", "Sub-form(Popover)": "하위 폼(팝업)", "Sub-details": "하위 세부사항", "Record picker": "레코드 선택기", "Toggles the subfield mode": "하위 필드 모드 전환", "Selector mode": "선택기 모드", "Subtable mode": "하위 테이블 모드", "Subform mode": "하위 폼 모드", "Field mode": "필드 모드", "Allow add new data": "새 데이터 추가 허용", "Edit block title": "블록 제목 편집", "Block title": "블록 제목", "Pattern": "패턴", "Operator": "연산자", "Editable": "편집 가능", "Readonly": "읽기 전용", "Easy-reading": "읽기 전용 (간편 모드)", "Add filter": "필터 추가", "Add filter group": "필터 그룹 추가", "Comparison": "비교", "is": "일치", "is not": "일치하지 않음", "contains": "포함", "does not contain": "포함하지 않음", "starts with": "시작 문자열", "not starts with": "시작하지 않음", "ends with": "끝나는 문자열", "not ends with": "끝나지 않음", "is empty": "비어 있음", "is not empty": "비어 있지 않음", "Edit chart": "차트 편집", "Add text": "텍스트 추가", "Filterable fields": "필터링 가능한 필드", "Edit button": "버튼 편집", "Hide": "숨기기", "Enable actions": "동작 활성화", "Import": "가져오기", "Export": "내보내기", "Customize": "사용자 정의", "Custom": "사용자 정의", "Function": "기능", "Popup form": "팝업 폼", "Flexible popup": "유연한 팝업", "Configure actions": "동작 구성", "Display order number": "표시 순서 번호", "Enable drag and drop sorting": "드래그 앤 드롭 정렬 활성화", "Triggered when the row is clicked": "행을 클릭할 때 트리거됨", "Add tab": "탭 추가", "Disable tabs": "탭 비활성화", "Details": "세부 정보", "Edit form": "폼 편집", "Create form": "폼 생성", "Form (Edit)": "폼 (편집)", "Form (Add new)": "폼 (새로 추가)", "Edit tab": "탭 편집", "Relationship blocks": "관계 데이터 블록", "Select record": "레코드 선택", "Display name": "표시 이름", "Select icon": "아이콘 선택", "Custom column name": "사용자 정의 열 이름", "Edit description": "설명 편집", "Required": "필수", "Unique": "중복 불가능", "Label field": "라벨 필드", "Default is the ID field": "기본값은 ID 필드입니다", "Set default sorting rules": "기본 정렬 규칙 설정", "Set validation rules": "유효성 검사 규칙 설정", "Max length": "최대 길이", "Min length": "최소 길이", "Maximum": "최대값", "Minimum": "최소값", "Max length must greater than min length": "최대 길이는 최소 길이보다 커야 합니다", "Min length must be less than max length": "최소 길이는 최대 길이보다 작아야 합니다", "Maximum must be greater than minimum": "최대값은 최소값보다 커야 합니다", "Minimum must be less than maximum": "최소값은 최대값보다 작아야 합니다", "Validation rule": "유효성 검사 규칙", "Add validation rule": "유효성 검사 규칙 추가", "Format": "형식", "Regular expression": "정규 표현식", "Error message": "오류 메시지", "Length": "길이", "The field value cannot be greater than ": "필드 값은 다음보다 클 수 없습니다: ", "The field value cannot be less than ": "필드 값은 다음보다 작을 수 없습니다: ", "The field value is not an integer number": "필드 값은 정수여야 합니다", "Set default value": "기본값 설정", "Default value": "기본값", "is before": "이전입니다", "is after": "이후입니다", "is on or after": "이후 또는 동일합니다", "is on or before": "이전 또는 동일합니다", "is between": "사이에 있습니다", "Upload": "업로드", "Select level": "수준 선택", "Province": "도", "City": "시", "Area": "구/군", "Street": "읍면동/거리", "Village": "마을/동", "Must select to the last level": "마지막 수준까지 선택해야 합니다", "Move {{title}} to": "{{title}}을(를) 다음 위치로 이동", "Target position": "대상 위치", "After": "이후", "Before": "이전", "Add {{type}} before {{title}}": "{{title}} 앞에 {{type}} 추가", "Add {{type}} after \"{{title}}\"": "\"{{title}}\" 뒤에 {{type}} 추가", "Add {{type}} in \"{{title}}\"": "\"{{title}}\" 안에 {{type}} 추가", "Original name": "원래 이름", "Custom name": "사용자 정의 이름", "Custom Title": "사용자 정의 제목", "Options": "옵션", "Option value": "옵션 값", "Option label": "옵션 레이블", "Color": "색상", "Add option": "옵션 추가", "Related collection": "관련 테이블", "Allow linking to multiple records": "여러 레코드에 연결 허용", "Daily": "매일", "Weekly": "매주", "Monthly": "매월", "Yearly": "매년", "Repeats": "반복", "Configure calendar": "달력 구성", "Title field": "제목 필드", "Custom title": "사용자 정의 제목", "Show lunar": "음력 표시", "Start date field": "시작 날짜 필드", "End date field": "종료 날짜 필드", "Navigate": "탐색", "Title": "제목", "Description": "설명", "Select view": "보기 선택", "Reset": "초기화", "Importable fields": "가능한 가져오기 필드", "Exportable fields": "가능한 내보내기 필드", "Saved successfully": "성공적으로 저장되었습니다", "Nickname": "닉네임", "Sign in": "로그인", "Sign in via account": "계정으로 로그인", "Sign in via phone": "전화로 로그인", "Create an account": "계정 생성", "Sign up": "가입", "Confirm password": "비밀번호 확인", "Log in with an existing account": "기존 계정으로 로그인", "Signed up successfully. It will jump to the login page.": "성공적으로 가입되었습니다. 로그인 페이지로 이동합니다.", "Password mismatch": "비밀번호 불일치", "Users": "사용자", "Verification code": "인증 코드", "Send code": "코드 전송", "Retry after {{count}} seconds": "{{count}} 초 후 재시도", "Must be 1-50 characters in length (excluding @.<>\"'/)": "1~50자여야 합니다 ( @.<>\"'/ 제외)", "Roles": "역할", "Add role": "역할 추가", "Role name": "역할 이름", "Configure": "구성", "Configure permissions": "권한 구성", "Edit role": "역할 편집", "Action permissions": "데이터 테이블 조작 권한", "Menu permissions": "메뉴 접근 권한", "Menu item name": "메뉴 항목 이름", "Allow access": "접근 허용", "Action name": "조작 이름", "Allow action": "조작 허용", "Action scope": "데이터 조작 범위", "Operate on new data": "새로운 데이터 조작", "Operate on existing data": "기존 데이터 조작", "Yes": "예", "No": "아니요", "Red": "빨강", "Magenta": "마젠타", "Volcano": "화산", "Orange": "주황", "Gold": "금색", "Lime": "라임", "Green": "초록", "Cyan": "청록", "Blue": "파랑", "Geek blue": "검정청", "Purple": "보라", "Default": "기본", "Add card": "카드 추가", "edit title": "제목 수정", "Turn pages": "페이지 넘김", "Others": "기타", "Other records": "기타 레코드", "Save as reference template": "참조 템플릿으로 저장", "Save as inherited template": "상속 템플릿으로 저장", "Save as block template": "블록 템플릿으로 저장", "Block templates": "블록 템플릿", "Block template": "블록 템플릿", "Convert reference to duplicate": "참조를 복제로 변환", "Template name": "템플릿 이름", "Block type": "블록 타입", "No blocks to connect": "연결할 블록 없음", "Action column": "조작 열", "Records per page": "페이지 당 레코드 수", "(Fields only)": "(필드만)", "Button title": "버튼 제목", "Button icon": "버튼 아이콘", "Submitted successfully": "성공적으로 제출되었습니다", "Operation succeeded": "작업 성공", "Operation failed": "작업 실패", "Open mode": "열기 모드", "Popup size": "팝업 크기", "Small": "작은", "Middle": "중간", "Large": "큰", "Size": "크기", "Oversized": "초대형", "Auto": "자동", "Object Fit": "객체 맞춤", "Cover": "커버", "Fill": "채우기", "Contain": "포함", "Scale Down": "축소", "Menu item title": "메뉴 항목 제목", "Menu item icon": "메뉴 항목 아이콘", "Target": "대상", "Position": "위치", "Insert before": "앞에 삽입", "Insert after": "뒤에 삽입", "UI Editor": "UI 편집기", "ASC": "오름차순", "DESC": "내림차순", "Add sort field": "정렬 필드 추가", "ID": "ID", "Identifier for program usage. Support letters, numbers and underscores, must start with an letter.": "프로그램 사용을 위한 식별자입니다. 문자, 숫자, 밑줄을 지원하며, 문자로 시작해야 합니다.", "Drawer": "서랍", "Dialog": "대화상자", "Delete action": "동작 삭제", "Custom column title": "사용자 정의 열 제목", "Column title": "열 제목", "Original title: ": "원래 제목: ", "Delete table column": "테이블 열 삭제", "Skip required validation": "필수 유효성 검사 건너뛰기", "Form values": "폼 값", "Fields values": "필드 값", "The field has been deleted": "필드가 삭제되었습니다", "When submitting the following fields, the saved values are": "다음 필드를 제출할 때 저장된 값은", "After successful submission": "제출 성공 후", "Then": "그런 다음", "Stay on current page": "현재 페이지에 머무르기", "Redirect to": "다음으로 리디렉션", "Save action": "동작 저장", "Exists": "존재", "Add condition": "조건 추가", "Add condition group": "조건 그룹 추가", "exists": "존재함", "not exists": "존재하지 않음", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "역할 UID", "Precision": "정밀도", "Rich Text": "리치 텍스트", "Junction collection": "중간 테이블", "Leave it blank, unless you need a custom intermediate table": "사용자 정의 중간 테이블이 필요하지 않은 경우 비워 두세요", "Fields": "필드", "Edit field title": "필드 제목 편집", "Field title": "필드 제목", "Original field title: ": "원래 필드 제목: ", "Edit tooltip": "툴팁 편집", "Delete field": "필드 삭제", "Select collection": "데이터 테이블 선택", "Blank block": "빈 블록", "Duplicate template": "템플릿 복제", "Reference template": "참조 템플릿", "Inherited template": "상속 템플릿", "Create calendar block": "캘린더 블록 생성", "Create kanban block": "칸반 블록 생성", "Grouping field": "그루핑 필드", "Single select and radio fields can be used as the grouping field": "단일 선택 및 라디오 필드는 그루핑 필드로 사용할 수 있습니다", "Tab name": "탭 이름", "Current record blocks": "현재 데이터 블록", "Popup message": "팝업 메시지", "Delete role": "역할 삭제", "Role display name": "역할 표시 이름", "Default role": "기본 역할", "All collections use general action permissions by default; permission configured individually will override the default one.": "모든 데이터 테이블은 기본적으로 일반적인 데이터 조작 권한을 사용합니다. 개별적으로 구성된 권한은 기본 권한을 무시합니다.", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "전체 시스템을 구성하는 것을 허용합니다. UI, 데이터 테이블, 권한 등을 포함합니다.", "New menu items are allowed to be accessed by default.": "새로운 메뉴 항목은 기본적으로 액세스할 수 있습니다.", "Global permissions": "전역 권한", "General permissions": "일반 권한", "Global action permissions": "전역 데이터 조작 권한", "General action permissions": "일반 데이터 조작 권한", "Plugin settings permissions": "플러그인 설정 권한", "Allow to design pages": "페이지 디자인 허용", "Allow to manage plugins": "플러그인 관리 허용", "Allow to configure plugins": "플러그인 구성 허용", "Allows to configure interface": "인터페이스 구성을 허용합니다", "Allows to install, activate, disable plugins": "플러그인을 설치, 활성화, 비활성화하는 것을 허용합니다", "Allows to configure plugins": "플러그인 구성을 허용합니다", "Action display name": "조작 표시 이름", "Allow": "허용", "Data scope": "데이터 범위", "Action on new records": "새로운 데이터 조작", "Action on existing records": "기존 데이터 조작", "All records": "모든 데이터", "Own records": "본인의 데이터", "Permission policy": "권한 정책", "Individual": "개별 설정", "General": "일반 설정", "Accessible": "접근 가능", "Configure permission": "권한 설정", "Action permission": "작업 권한", "Field permission": "필드 권한", "Scope name": "데이터 범위 이름", "Unsaved changes": "저장되지 않은 변경", "Are you sure you don't want to save?": "저장하지 않으려고 하시겠습니까?", "Dragging": "드래깅 중", "Popup": "팝업", "Trigger workflow": "워크플로우 트리거", "Request API": "API 요청", "Assign field values": "필드 값 할당", "Constant value": "상수 값", "Dynamic value": "동적 값", "Current user": "현재 사용자", "Current role": "현재 역할", "Current record": "현재 레코드", "Current collection": "현재 데이터 테이블", "Other collections": "기타 데이터 테이블", "Current popup record": "현재 팝업 레코드", "Parent popup record": "상위 팝업 레코드", "Associated records": "관련 레코드", "Parent record": "상위 레코드", "Current time": "현재 시간", "Now": "지금", "Popup close method": "팝업 닫기 방법", "Automatic close": "자동 닫기", "Manually close": "수동 닫기", "After successful update": "성공적인 업데이트 후", "Save record": "레코드 저장", "Updated successfully": "성공적으로 업데이트됨", "After successful save": "성공적인 저장 후", "After clicking the custom button, the following field values will be assigned according to the following form.": "사용자 정의 버튼을 클릭한 후 다음 필드 값이 다음 양식에 따라 할당됩니다.", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "사용자 정의 버튼을 클릭한 후 현재 레코드의 다음 필드가 다음 양식에 따라 저장됩니다.", "Button background color": "버튼 배경색", "Highlight": "강조", "Danger red": "위험 빨간색", "Custom request": "사용자 정의 요청", "Request settings": "요청 설정", "Request URL": "요청 URL", "Request method": "요청 방법", "Request query parameters": "요청 쿼리 매개변수(JSON 형식)", "Request headers": "요청 헤더(JSON 형식)", "Request body": "요청 본문(JSON 형식)", "Request success": "요청 성공", "Invalid JSON format": "잘못된 JSON 형식", "After successful request": "성공적인 요청 후", "Add exportable field": "내보낼 수 있는 필드 추가", "Audit logs": "감사 로그", "Record ID": "레코드 ID", "User": "사용자", "Field": "필드", "Select": "선택", "Select field": "필드 선택", "Field value changes": "필드 값 변경", "One to one (has one)": "일대일 (has one)", "One to one (belongs to)": "일대일 (belongs to)", "Use the same time zone (GMT) for all users": "모든 사용자에 대해 동일한 시간대(GMT) 사용", "Province/city/area name": "도시/지역/구 이름", "Enabled languages": "사용 가능한 언어", "View all plugins": "모든 플러그인 보기", "Print": "인쇄", "Done": "완료", "Sign up successfully, and automatically jump to the sign-in page": "가입이 성공하면 자동으로 로그인 페이지로 이동합니다.", "ACL": "액세스 제어 목록", "Collection manager": "데이터 테이블 관리자", "Plugin manager": "플러그인 관리자", "Local": "로컬", "Built-in": "내장", "Marketplace": "마켓플레이스", "Add plugin": "플러그인 추가", "Upgrade": "업그레이드", "Plugin dependencies check failed": "플러그인 종속성 검사 실패", "Remove": "제거", "Docs": "문서", "More details": "자세히", "Upload new version": "새 버전 업로드", "Official plugin": "공식 플러그인", "Version": "버전", "Npm package": "Npm 패키지", "Upload plugin": "플러그인 업로드", "Npm package name": "Npm 패키지 이름", "Add type": "유형 추가", "Plugin source": "플러그인 소스", "Changelog": "변경 로그", "Dependencies check": "종속성 검사", "Update plugin": "플러그인 업데이트", "Installing": "설치 중", "The deletion was successful.": "삭제가 성공했습니다.", "Plugin Zip File": "플러그인 압축 파일", "Compressed file URL": "압축 파일 URL", "Last updated": "최근 업데이트", "PackageName": "패키지 이름", "DisplayName": "표시 이름", "Readme": "<PERSON><PERSON>", "Dependencies compatibility check": "종속성 호환성 검사", "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.": "플러그인 종속성 검사 실패, 종속 버전을 변경하여 버전 요구 사항을 충족해야 합니다.", "Version range": "버전 범위", "Plugin's version": "플러그인 버전", "Result": "결과", "No CHANGELOG.md file": "CHANGELOG.md 파일이 없습니다", "No README.md file": "README.md 파일이 없습니다", "Homepage": "홈페이지", "Drag and drop the file here or click to upload, file size should not exceed 30M": "파일을 여기로 끌어다 놓거나 클릭하여 업로드하십시오. 파일 크기는 30M를 초과하지 않아야 합니다", "Dependencies check failed, can't enable.": "종속성 검사 실패, 활성화할 수 없습니다.", "Plugin starting...": "플러그인 시작 중...", "Plugin stopping...": "플러그인 중지 중...", "Are you sure to delete this plugin?": "이 플러그인을 삭제하시겠습니까?", "re-download file": "파일 다시 다운로드", "Not enabled": "활성화되지 않음", "Search plugin": "플러그인 검색", "Author": "작성자", "Plugin loading failed. Please check the server logs.": "플러그인 로드 실패. 서버 로그를 확인하십시오.", "Coming soon...": "곧 출시됩니다...", "All plugin settings": "모든 플러그인 설정", "Bookmark": "책갈피", "Manage all settings": "모든 설정 관리", "Create inverse field in the target collection": "대상 데이터 테이블에 반전 관계 필드 생성", "Inverse field name": "반전 관계 필드 이름", "Inverse field display name": "반전 관계 필드 표시 이름", "Bulk update": "대량 업데이트", "After successful bulk update": "대량 업데이트 성공 후", "Bulk edit": "대량 편집", "Data will be updated": "데이터가 업데이트됩니다", "Selected": "선택됨", "All": "모두", "Update selected data?": "선택한 데이터를 업데이트하시겠습니까?", "Update all data?": "모든 데이터를 업데이트하시겠습니까?", "Remains the same": "변경 없음", "Changed to": "변경됨", "Clear": "지우기", "Add attach": "첨부 추가", "Please select the records to be updated": "업데이트할 레코드를 선택하십시오", "Selector": "선택기", "Inner": "내부", "Search and select collection": "검색 및 데이터 테이블 선택", "Please fill in the iframe URL": "iframe URL을 입력하십시오", "Fix block": "블록 고정", "Plugin name": "플러그인 이름", "Plugin tab name": "플러그인 탭 이름", "Enable page header": "페이지 헤더 활성화", "Display page title": "페이지 제목 표시", "Edit page title": "페이지 제목 편집", "Enable page tabs": "페이지 탭 활성화", "Enable link": "링크 활성화", "Column width": "열 너비", "Sortable": "정렬 가능", "Constant": "상수", "Select a variable": "변수 선택", "Insert": "삽입", "Insert if not exists": "존재하지 않을 때 삽입", "Insert if not exists, or update": "존재하지 않을 때 삽입하거나 업데이트", "System variables": "시스템 변수", "Date variables": "날짜 변수", "Double click to choose entire object": "전체 객체 선택하려면 더블 클릭", "True": "참", "False": "거짓", "Prettify": "예쁘게 표시", "Theme": "테마", "Default theme": "기본 테마", "Compact theme": "간소한 테마", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "이것은 아마도 NocoBase 내부 버그입니다. <1>여기</1>에서 문제를 열어주시기 바랍니다.", "Render Failed": "렌더링 실패", "Feedback": "피드백", "Try again": "다시 시도", "Click or drag file to this area to upload": "업로드하려면 파일을 클릭하거나 끌어다 놓으세요", "Support for a single or bulk upload, file size should not exceed": "단일 또는 대량 업로드 지원, 파일 크기는 초과하면 안 됨", "Default title for each record": "각 레코드의 기본 제목", "If collection inherits, choose inherited collections as templates": "컬렉션이 상속되면 상속된 컬렉션을 템플릿으로 선택", "Select an existing piece of data as the initialization data for the form": "폼의 초기화 데이터로 기존 데이터를 선택", "Only the selected fields will be used as the initialization data for the form": "폼의 초기화 데이터로 선택한 필드만 사용", "Template Data": "템플릿 데이터", "Data fields": "데이터 필드", "Add template": "템플릿 추가", "Display data template selector": "데이터 템플릿 선택기 표시", "Form data templates": "폼 데이터 템플릿", "Data template": "데이터 템플릿", "No configuration available.": "구성을 사용할 수 없습니다.", "Reload application": "애플리케이션 다시 로드", "The application is reloading, please do not close the page.": "애플리케이션이 다시 로드되고 있습니다. 페이지를 닫지 마십시오.", "Application reloading": "애플리케이션 다시 로드 중", "Restart application": "애플리케이션 다시 시작", "Allows to clear cache, reboot application": "캐시 지우기, 애플리케이션 재시작 허용", "The will interrupt service, it may take a few seconds to restart. Are you sure to continue?": "이는 서비스를 중단시킬 것이며 재시작에는 몇 초 정도 소요될 수 있습니다. 계속 진행하시겠습니까?", "Restart": "재시작", "Clear cache": "캐시 지우기", "Duplicate": "복제", "Duplicating": "복제 중", "Duplicate mode": "복제 모드", "Quick duplicate": "빠른 복제", "Duplicate and continue": "복제하고 계속하기", "Please configure the duplicate fields": "请配置要复制的字段", "Add": "추가", "Add new mode": "새로운 추가 방식", "Quick add": "빠른 추가", "Modal add": "모달 추가", "Save mode": "저장 방식", "First or create": "처음 또는 생성", "Update or create": "업데이트 또는 생성", "Find by the following fields": "다음 필드로 찾기", "Create": "생성", "Current form": "현재 폼", "Current object": "현재 객체", "Quick create": "빠른 생성", "Dropdown": "드롭다운", "Pop-up": "팝업", "File manager": "파일 매니저", "Direct duplicate": "직접 복제", "Copy into the form and continue to fill in": "폼에 복사하고 계속 채우기", "Linkage with form fields": "폼 필드와의 연동", "App error": "앱 오류", "Failed to load plugin": "플러그인 로드 실패", "Allow add new, update and delete actions": "추가, 업데이트 및 삭제 작업 허용", "Date display format": "날짜 표시 형식", "Assign data scope for the template": "템플릿에 데이터 범위 할당", "Table selected records": "테이블에서 선택한 레코드", "Tag": "태그", "Tag color field": "태그 색상 필드", "Sync successfully": "동기화 성공", "Sync from form fields": "폼 필드에서 동기화", "Select all": "모두 선택", "UnSelect all": "모두 선택 해제", "Determine whether a record exists by the following fields": "다음 필드로 레코드의 존재 여부 결정", "Cascade Select": "계층식 선택", "Execute": "실행", "Please use a valid SELECT or WITH AS statement": "유효한 SELECT 또는 WITH AS 문을 사용하세요", "Please confirm the SQL statement first": "먼저 SQL 문을 확인하세요", "Automatically drop objects that depend on the collection (such as views), and in turn all objects that depend on those objects": "컬렉션에 의존하는 객체(예: 뷰)와 이러한 객체에 의존하는 모든 객체를 자동으로 삭제합니다", "Secondary confirmation": "두 번째 확인", "Perform the {{title}}": "{{title}} 실행", "Are you sure you want to perform the {{title}} action?": "{{title}} 작업을 실행하시겠습니까?", "Sign in with another account": "다른 계정으로 로그인", "Return to the main application": "메인 애플리케이션으로 돌아가기", "Permission denied": "허가 거부", "Allow add new": "신규 추가 허용", "Allow selection of existing records": "기존 레코드 선택 허용", "loading": "로드 중", "name is required": "이름은 필수입니다", "data source": "데이터 소스", "Data source": "데이터 소스", "DataSource": "데이터 소스", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "{{type}} \"{{name}}\"이(가) 삭제되었을 수 있습니다. 이 {{blockType}}을(를) 제거하십시오.", "Home page": "홈페이지", "Handbook": "사용자 매뉴얼", "License": "라이선스", "This variable has been deprecated and can be replaced with \"Current form\"": "변수가 폐기되었습니다. \"현재 폼\"을 대체로 사용할 수 있습니다", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "이 변수의 값은 페이지 URL의 쿼리 문자열에서 파생됩니다. 이 변수는 페이지에 쿼리 문자열이 있는 경우에만 정상적으로 사용할 수 있습니다.", "URL search params": "URL 검색 매개변수", "Expand All": "모두 펼치기", "Clear default value": "기본값 지우기", "Open in new window": "새 창에서 열기", "Sorry, the page you visited does not exist.": "죄송합니다. 방문한 페이지가 존재하지 않습니다.", "Allow multiple selection": "다중 선택 허용", "Parent object": "부모 객체", "Ellipsis overflow content": "생략 부호로 내용 줄임", "Hide column": "열 숨기기", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "구성 모드에서는 전체 열이 투명해집니다. 비구성 모드에서는 전체 열이 숨겨집니다. 전체 열이 숨겨져도 구성된 기본값 및 기타 설정은 여전히 적용됩니다.", "Desktop routes": "데스크톱 라우트", "Route permissions": "라우트 권한", "New routes are allowed to be accessed by default": "새로운 라우트는 기본적으로 액세스할 수 있습니다", "Route name": "라우트 이름", "Mobile routes": "모바일 라우트", "Show in menu": "메뉴에 표시", "Hide in menu": "메뉴에 숨기기", "Path": "경로", "Type": "유형", "Access": "액세스", "Routes": "라우트", "Add child route": "하위 라우트 추가", "Delete routes": "라우트 삭제", "Delete route": "라우트 삭제", "Are you sure you want to hide these routes in menu?": "이 라우트를 메뉴에 숨기시겠습니까?", "Are you sure you want to show these routes in menu?": "이 라우트를 메뉴에 표시하시겠습니까?", "Are you sure you want to hide this menu?": "이 메뉴를 숨기시겠습니까?", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "숨기면 이 메뉴는 메뉴 바에 더 이상 표시되지 않습니다. 다시 표시하려면 라우트 관리 페이지에서 설정해야 합니다.", "If selected, the page will display Tab pages.": "선택되면 페이지는 탭 페이지를 표시합니다.", "If selected, the route will be displayed in the menu.": "선택되면 라우트는 메뉴에 표시됩니다.", "Are you sure you want to hide this tab?": "이 탭을 숨기시겠습니까?", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "숨기면 이 탭은 탭 바에 더 이상 표시되지 않습니다. 다시 표시하려면 라우트 관리 페이지에서 설정해야 합니다.", "No pages yet, please configure first": "아직 페이지가 없습니다. 먼저 설정하십시오", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "사용자 인터페이스 편집기 모드에 들어가려면 오른쪽 상단의 \"UI 편집기\" 아이콘을 클릭하십시오", "Deprecated": "사용 중단됨", "Full permissions": "모든 권한", "Refresh data blocks": "데이터 블록 새로 고침", "Select data blocks to refresh": "데이터 블록을 선택하여 새로 고침", "After successful submission, the selected data blocks will be automatically refreshed.": "전송 후, 선택한 데이터 블록이 자동으로 새로 고쳐집니다.", "Reset link expiration": "링크 만료 재설정"}