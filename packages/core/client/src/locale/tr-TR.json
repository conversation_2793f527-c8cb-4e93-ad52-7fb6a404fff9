{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "Her sayfada <1><0>10</0><1>20</1><2>50</2><3>100</3></1> adet gösterim", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "Meet <1><0>All</0><1>Any</1></1> conditions in the group", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "Açılış Şekli<1><0>Popup Ekran</0><1>Yan <PERSON></1><2>Sayfa</2></1>", "{{count}} filter items": "{{count}} filtrelenmiş ö<PERSON>e", "{{count}} more items": "{{count}} <PERSON><PERSON><PERSON>ha", "Total {{count}} items": "Toplam {{count}} adet <PERSON>", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Yesterday": "<PERSON><PERSON><PERSON>", "Tomorrow": "<PERSON><PERSON><PERSON><PERSON>", "Month": "Ay", "Week": "<PERSON><PERSON><PERSON>", "This week": "<PERSON><PERSON><PERSON>", "Next week": "Gelecek Hafta", "This month": "<PERSON><PERSON>", "Next month": "Gelecek Ay", "Last quarter": "<PERSON><PERSON><PERSON><PERSON>", "This quarter": "<PERSON><PERSON>", "Next quarter": "Gelecek <PERSON>", "This year": "<PERSON><PERSON>", "Next year": "Gelecek Yıl", "Last week": "Geçen Hafta", "Last month": "Geçen Ay", "Last year": "Geçen Yıl", "Last 7 days": "Son 7 Gün", "Last 30 days": "Son 30 Gün", "Last 90 days": "Son 90 Gün", "Next 7 days": "Sonraki 7 Gün", "Next 30 days": "Sonraki 30 Gün", "Next 90 days": "Sonraki 90 Gün", "Work week": "Çalışma Haftası", "Day": "<PERSON><PERSON><PERSON>", "Agenda": "<PERSON><PERSON><PERSON>", "Date": "<PERSON><PERSON><PERSON>", "Time": "Saat", "Event": "<PERSON><PERSON>", "None": "Boş", "Unconnected": "Bağlantı yok", "System settings": "Sistem ayarları", "System title": "Sistem başlığı", "Logo": "Logo", "Add menu item": "Menüye öğe e<PERSON>", "Page": "Say<PERSON>", "Tab": "Sekme", "Name": "Ad<PERSON>", "Icon": "İkon", "Group": "Grup", "Link": "Link", "Save conditions": "Koşulları kaydet", "Edit menu item": "<PERSON><PERSON> d<PERSON>", "Move to": "Taşınacak yer", "Insert left": "<PERSON><PERSON>", "Insert right": "<PERSON><PERSON><PERSON>", "Insert inner": "<PERSON><PERSON><PERSON>", "Delete": "Sil", "Disassociate": "Bağlantıyı kes", "Disassociate record": "Kaydı bağlantıyı kes", "Are you sure you want to disassociate it?": "Bağlantıyı kesmek istediğinizden emin misiniz?", "UI editor": "UI editor", "Collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Collections & Fields": "Koleksiyonlar & Alanlar", "Roles & Permissions": "Roller & Yetkilendirmeler", "Edit profile": "<PERSON><PERSON>", "Change password": "<PERSON><PERSON><PERSON>", "Old password": "Eski şifre", "New password": "<PERSON><PERSON>", "Switch role": "Rol değiştir", "Super admin": "<PERSON><PERSON><PERSON> admin", "Language": "Dil", "Allow sign up": "<PERSON><PERSON><PERSON> olmaya izin ver", "Sign out": "Çıkış yap", "Cancel": "İptal", "Submit": "<PERSON><PERSON><PERSON>", "Close": "Ka<PERSON><PERSON>", "Set the data scope": "<PERSON><PERSON> kapsamını ayarla", "Data loading mode": "<PERSON><PERSON> modu", "Set data loading mode": "<PERSON><PERSON><PERSON> modunu a<PERSON>la", "Load all data when filter is empty": "Fi<PERSON>re boş olduğunda tüm verileri yükle", "Do not load data when filter is empty": "Filtre boş olduğunda veri yükleme", "Data blocks": "Veri Blokları", "Filter blocks": "Filtre blokları", "Table": "<PERSON><PERSON><PERSON>", "Form": "Form", "Collapse": "Dar<PERSON><PERSON>", "Select data source": "Veri kaynağını seç", "Calendar": "Takvim", "Kanban": "Ka<PERSON><PERSON>", "Select grouping field": "Gruplandırma alanını seç", "Media": "<PERSON><PERSON><PERSON>", "Markdown": "İşaretle", "Wysiwyg": "Wysiwyg", "Chart blocks": "Grafik blokları", "Column chart": "<PERSON><PERSON><PERSON> g<PERSON>", "Bar chart": "<PERSON><PERSON>", "Line chart": "Çizgi grafik", "Pie chart": "Pasta grafik", "Area chart": "<PERSON>", "Other chart": "<PERSON><PERSON><PERSON> g<PERSON>", "Other blocks": "<PERSON><PERSON><PERSON>", "In configuration": "Yapılandırmada", "Chart title": "Grafik başlığı", "Chart type": "<PERSON><PERSON>ü<PERSON>", "Chart config": "Grafik yapılandırması", "Templates": "Şablonlar", "Template": "Şablon", "Select template": "Şablon seç", "Action logs": "<PERSON><PERSON><PERSON>", "Create template": "Şablon oluştur", "Edit markdown": "İşaretlemeyi düzenle", "Add block": "Blok ekle", "Add new": "<PERSON><PERSON>", "Add record": "<PERSON><PERSON><PERSON>", "Custom field display name": "<PERSON><PERSON> alan <PERSON> adı", "Display fields": "Koleksiyon alanlarını görüntüle", "Edit record": "Kaydı düzenle", "Delete menu item": "Menü öğesini sil", "Add page": "<PERSON><PERSON>", "Add group": "Grup ekle", "Add link": "<PERSON> e<PERSON>", "Insert above": "<PERSON><PERSON><PERSON><PERSON> ekle", "Insert below": "Aşağ<PERSON>ya ekle", "Save": "<PERSON><PERSON>", "Delete block": "<PERSON><PERSON><PERSON><PERSON> sil", "Are you sure you want to delete it?": "<PERSON><PERSON>ek istediğinizden emin misiniz?", "This is a demo text, **supports Markdown syntax**.": "Bu bir örnek yazıdır, **işaretleme yazısı destekleniyor**.", "Filter": "Filtre", "Connect data blocks": "Veri bloklarını bağla", "Action type": "İşlem Türü", "Actions": "İşlemler", "Insert": "<PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON>", "View": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View record": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Refresh": "<PERSON><PERSON><PERSON>", "Data changes": "<PERSON><PERSON>şik<PERSON>leri", "Field name": "<PERSON>", "Before change": "Değiştirmeden önce", "After change": "Değiştirdikten sonra", "Delete record": "Kaydı sil", "Create collection": "Koleksiyon oluştur", "Collection display name": "Koleksiyon görünen adı", "Collection name": "Koleksiyon adı", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.": "Rastgele oluşturulur ve değiştirilebilir. Desteklenen içerik; harf<PERSON>, sayılar ve alt çizgiler. B<PERSON> harfle başlamalıdır.", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit collection": "Koleksiyon dü<PERSON>le", "Configure fields": "Alanları düzenle", "Configure columns": "Sütunları yapılandır", "Edit field": "<PERSON>", "Configure fields of {{title}}": "{{title}} alanların<PERSON> düzenle", "Basic": "Temel", "Single line text": "Tek satırlık metin", "Long text": "<PERSON><PERSON><PERSON> metin", "Phone": "Telefon", "Email": "Eposta", "Number": "<PERSON><PERSON><PERSON>", "Integer": "Tamsayı", "Percent": "<PERSON><PERSON>z<PERSON>", "Password": "Şifre", "Advanced type": "Gelişmiş", "Formula": "<PERSON><PERSON><PERSON>", "Formula description": "<PERSON><PERSON>ı kayıttaki diğer alanlara dayalı olarak her kayıtta bir değer hesa<PERSON>n.", "Choices": "<PERSON><PERSON><PERSON><PERSON>", "Checkbox": "Checkbox", "Single select": "Selectbox", "Multiple select": "Multi Select", "Radio group": "Radio Seçim grup", "Checkbox group": "Checkbox grup", "China region": "<PERSON><PERSON>", "Date & Time": "Tarih & Saat", "Datetime": "Datetime", "Relation": "Relation", "Link to": "Link to", "Link to description": "Used to create collection relationships quickly and compatible with most common scenarios. Suitable for non-developer use. When present as a field, it is a drop-down selection used to select records from the target collection. Once created, it will simultaneously generate the associated fields of the current collection in the target collection.", "Sub-table": "Alt-tablo", "System info": "Sistem bilgisi", "Created at": "Oluşturulma zamanı", "Last updated at": "<PERSON> g<PERSON><PERSON><PERSON><PERSON> z<PERSON>ı", "Created by": "Oluşturan kişi", "Last updated by": "Son güncelleyen kişi", "Add field": "<PERSON>", "Field display name": "<PERSON> adı", "Field type": "<PERSON>", "Field interface": "<PERSON>", "Title field": "Başlık alanı", "Date format": "<PERSON><PERSON><PERSON> formatı", "Year/Month/Day": "Yıl/Ay/Gün", "Year-Month-Day": "Yıl-Ay-Gün", "Day/Month/Year": "Gün/Ay/Yıl", "Show time": "Zamanı göster", "Time format": "Zaman formatı", "12 hour": "12 saat", "24 hour": "24 saat", "Relationship type": "Bağlantı türü", "Source collection": "Kaynak k<PERSON>iyon", "Source key": "<PERSON><PERSON><PERSON>", "Target collection": "<PERSON><PERSON><PERSON>", "Through collection": "<PERSON><PERSON>a k<PERSON>", "Target key": "<PERSON><PERSON><PERSON>", "Foreign key": "Yabancı anahtar", "One to one": "Bire-Bir", "One to many": "Bire-Çok", "Many to one": "Çoka-Bir", "Many to many": "Çoka-Çok", "One to one description": "Bire bir ilişkiler oluşturmak için kullanılır. <PERSON><PERSON><PERSON><PERSON>, bir kullanıcının bir profili vardır.", "One to many description": "Bire çok ilişkisi oluşturmak için kullanılır. <PERSON><PERSON><PERSON><PERSON>, bir ülkenin birçok şehri olacaktır ve bir şehir yalnızca bir ülkede olabilir. Alan olarak mevcut olduğunda, iliş<PERSON>li koleksiyonun kayıtlarını görüntüleyen bir alt tablodur. Oluşturulduğunda, ilişkili koleksiyonda bir Çoktan bire alanı otomatik olarak oluşturulur.", "Many to one description": "Çoktan bire ilişkiler oluşturmak için kullanılır. <PERSON><PERSON><PERSON><PERSON>, bir şehir sadece bir ülkeye ait olabilir ve bir ülkenin birçok şehri olabilir. Bir alan olarak mevcut olduğunda, ilişkili koleksiyondan kayıt seçmek için kullanılan bir açılır seçimdir. Oluşturulduktan sonra, ilişkili koleksiyonda bire çok alanı otomatik olarak oluşturulur.", "Many to many description": "Çoktan çoğa ilişkiler oluşturmak için kullanılır. <PERSON><PERSON><PERSON><PERSON>, bir öğrencinin birçok öğretmeni olacak ve bir öğretmenin birçok öğrencisi olacaktır. Bir alan olarak mevcut olduğunda, ilişkili koleksiyondan kayıtları seçmek için kullanılan bir açılır seçimdir.", "Foreign key 1": "Yabancı anahtar 1", "Foreign key 2": "Yabancı anahtar 2", "Add filter": "Filtre ekle", "Add filter group": "Filtre grubu ekle", "Comparision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is": "<PERSON><PERSON><PERSON><PERSON>", "is not": "<PERSON><PERSON><PERSON>", "contains": "<PERSON><PERSON><PERSON><PERSON>", "does not contain": "içermez", "starts with": "ile b<PERSON><PERSON><PERSON>", "not starts with": "ile b<PERSON><PERSON><PERSON>az", "ends with": "ile biter", "not ends with": "ile bitmez", "is empty": "boş", "is not empty": "b<PERSON><PERSON>", "Edit chart": "<PERSON><PERSON><PERSON><PERSON>", "Add text": "<PERSON><PERSON> e<PERSON>", "Filterable fields": "Filtrelenebil<PERSON>", "Edit button": "<PERSON><PERSON><PERSON><PERSON> but<PERSON>", "Hide": "<PERSON><PERSON><PERSON>", "Enable actions": "İşlemleri aktifleştir", "Export": "Dışarı aktar", "Customize": "Ö<PERSON>leş<PERSON>r", "Function": "Fonksiyon", "Popup form": "Popup form", "Flexible popup": "Esnek popup", "Configure actions": "İşlemleri yapılandır", "Display order number": "<PERSON><PERSON><PERSON> numarasını göster", "Enable drag and drop sorting": "Sürükle-Bırak sıralamayı aktif et", "Triggered when the row is clicked": "Satır tıklandığında tetiklenir", "Add tab": "<PERSON><PERSON><PERSON> e<PERSON>", "Disable tabs": "<PERSON><PERSON><PERSON><PERSON>", "Details": "Detaylar", "Edit form": "<PERSON><PERSON>", "Create form": "Form oluştur", "Form (Edit)": "Form (Düzenle)", "Form (Add new)": "Form (<PERSON><PERSON>)", "Edit tab": "<PERSON><PERSON><PERSON>", "Relationship blocks": "İlişki blokları", "Select record": "<PERSON><PERSON><PERSON> seç", "Display name": "Görünen ad", "Select icon": "İkon seç", "Custom column name": "<PERSON><PERSON> sütun adı", "Edit description": "Açıklamayı düzenle", "Required": "Zorun<PERSON>", "Label field": "Etiket alanı", "Default is the ID field": "Varsayılan ID alanıdır", "Set default sorting rules": "Varsayılan sıralama kurallarını ayarla", "is before": "önce", "is after": "sonra", "is on or after": "açık veya sonra", "is on or before": "açık veya önce", "is between": "aralık", "Upload": "<PERSON><PERSON><PERSON>", "Select level": "<PERSON><PERSON><PERSON> seç", "Province": "<PERSON><PERSON><PERSON>", "City": "Şehir", "Area": "<PERSON>", "Street": "Cadde", "Village": "<PERSON><PERSON><PERSON>", "Must select to the last level": "Son seviyeye kadar seç<PERSON>ir", "Move {{title}} to": "{{title}} başlığını taşınacağı yer", "Target position": "<PERSON><PERSON><PERSON>", "After": "Sonra", "Before": "Önce", "Add {{type}} before \"{{title}}\"": "\"{{title}}\" ba<PERSON><PERSON>ığından önce {{type}} ekle", "Add {{type}} after \"{{title}}\"": "\"{{title}}\" ba<PERSON><PERSON><PERSON>ğından sonra {{type}} ekle", "Add {{type}} in \"{{title}}\"": "\"{{title}}\" ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n içine {{type}} ekle", "Original name": "<PERSON><PERSON><PERSON> isim", "Custom name": "<PERSON><PERSON> isim", "Custom Title": "Özel Başlık", "Options": "Seçenekler", "Option value": "Seçenek değeri", "Option label": "Seçenek etiketi", "Color": "Renk", "Add option": "Seçenek ekle", "Related collection": "Bağlantılı koleksiyon", "Allow linking to multiple records": "Birden çok kayda bağlanmaya izin ver", "Configure calendar": "<PERSON>k<PERSON><PERSON>ı<PERSON>", "Start date field": "Başlangıç tarihi alanı", "End date field": "Bitiş tarihi alanı", "Navigate": "Navigate", "Title": "Başlık", "Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "Select view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seç", "Reset": "Sıfırla", "Exportable fields": "Dışa aktarılabilir alanlar", "Saved successfully": "Başarıyla ka<PERSON>il<PERSON>", "Nickname": "Rumuz", "Sign in": "<PERSON><PERSON><PERSON> yap", "Create an account": "<PERSON><PERSON><PERSON>", "Sign up": "<PERSON><PERSON><PERSON>", "Confirm password": "<PERSON><PERSON><PERSON>", "Log in with an existing account": "Mevcut bir hesapla giriş ya<PERSON>ın", "Signed up successfully. It will jump to the login page.": "Başarıyla kaydoldu. G<PERSON>ş sayfasına yönlendirileceksiniz.", "Password mismatch": "<PERSON><PERSON><PERSON> e<PERSON>şmiyor", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Roles": "Roller", "Add role": "<PERSON><PERSON>", "Role name": "Rol adı", "Configure": "Yapılandır", "Configure permissions": "Yetkileri yapılandır", "Edit role": "<PERSON><PERSON>", "Action permissions": "İşlem yetkileri", "Menu permissions": "<PERSON><PERSON>", "Menu item name": "Menü öğe adı", "Allow access": "<PERSON><PERSON><PERSON><PERSON> izin ver", "Action name": "İşlem adı", "Allow action": "İşleme izin ver", "Action scope": "İşlem kapsamı", "Operate on new data": "<PERSON><PERSON> verilerle ç<PERSON>ı<PERSON>", "Operate on existing data": "<PERSON>ar olan verilerle ç<PERSON>ış", "Yes": "<PERSON><PERSON>", "No": "Hay<PERSON><PERSON>", "Red": "Kırmızı", "Magenta": "Macenta", "Volcano": "Volkan", "Orange": "<PERSON><PERSON><PERSON>", "Gold": "Altı<PERSON>", "Lime": "Limon sarısı", "Green": "<PERSON><PERSON><PERSON>", "Cyan": "Camgöbeği", "Blue": "<PERSON><PERSON>", "Geek blue": "Geek blue", "Purple": "<PERSON><PERSON>", "Default": "Varsayılan", "Add card": "<PERSON><PERSON>", "edit title": "baş<PERSON><PERSON><PERSON><PERSON> düzenle", "Turn pages": "Sayfaları çevir", "Others": "Di<PERSON><PERSON><PERSON><PERSON>", "Other records": "<PERSON><PERSON><PERSON>", "Save as reference template": "Referans şablonu olarak ka<PERSON>et", "Save as inherited template": "Kalıtım şablonu olarak kaydet", "Save as block template": "Blok şablonu olarak kaydet", "Block templates": "Blok şablonları", "Block template": "Blok şablonu", "Convert reference to duplicate": "Referansı kopyaya dönüştür", "Template name": "Şablon adı", "Block type": "Blok türü", "No blocks to connect": "Bağlanacak blok yok", "Action column": "İşlem sütunu", "Records per page": "<PERSON><PERSON> ba<PERSON><PERSON>na ka<PERSON>ıt", "(Fields only)": "(<PERSON><PERSON><PERSON>)", "Button title": "<PERSON><PERSON> etiketi", "Button icon": "<PERSON><PERSON> ikonu", "Submitted successfully": "Başarıyla gönderildi", "Operation succeeded": "Operasyon başarılı", "Operation failed": "Operasyon başarısız", "Open mode": "Açılış türü", "Menu item title": "Menü öğe başlığı", "Menu item icon": "Menü öğe ikonu", "Target": "<PERSON><PERSON><PERSON>", "Position": "Pozisyon", "Insert before": "<PERSON><PERSON><PERSON>", "Insert after": "<PERSON><PERSON><PERSON><PERSON>", "UI Editor": "UI Editor", "ASC": "<PERSON><PERSON>", "DESC": "<PERSON><PERSON><PERSON>", "Add sort field": "S<PERSON>ralama alanı ekle", "ID": "ID", "Drawer": "Çekmece", "Dialog": "<PERSON><PERSON><PERSON>", "Delete action": "İşlemi sil", "Custom column title": "<PERSON>zel sütun başlığı", "Column title": "<PERSON><PERSON><PERSON> başlığı", "Original title: ": "Orjinal başlık: ", "Delete table column": "<PERSON><PERSON><PERSON> s<PERSON> sil", "Skip required validation": "<PERSON><PERSON><PERSON><PERSON> alan kontroll<PERSON>ni atla", "Form values": "Form değerleri", "Fields values": "<PERSON><PERSON><PERSON><PERSON>", "The field has been deleted": "<PERSON>", "When submitting the following fields, the saved values are": "Aşağıdaki alanlar gönderilirken kaydedilen <PERSON>", "After successful submission": "Başarılı gönderimden sonra", "Then": "Then", "Stay on current page": "Geç<PERSON><PERSON> kal", "Redirect to": "Yönlendirilecek yer", "Save action": "<PERSON><PERSON>", "Exists": "<PERSON><PERSON> o<PERSON>", "Add condition": "Koşul ekle", "Add condition group": "Koşul grubu ekle", "exists": "var olanlar", "not exists": "var olmayanlar", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "Rol UID", "Precision": "Ke<PERSON>lik", "Formula mode": "<PERSON><PERSON><PERSON> modu", "Expression": "Expression", "Input +, -, *, /, ( ) to calculate, input @ to open field variables.": "Input +, -, *, /, ( ) to calculate, input @ to open field variables.", "Formula error.": "<PERSON><PERSON><PERSON> hatal<PERSON>.", "Rich Text": "<PERSON><PERSON>", "Junction collection": "Bağlantı koleksiyonu", "Leave it blank, unless you need a custom intermediate table": "Özel bir ara tabloya ihtiyacınız yoksa boş bırakın", "Fields": "<PERSON><PERSON>", "Edit field title": "<PERSON> ba<PERSON><PERSON><PERSON>ğı<PERSON><PERSON> düzenle", "Field title": "<PERSON> ba<PERSON>", "Original field title: ": "Orijinal alan b<PERSON>: ", "Edit tooltip": "İpucunu <PERSON>", "Delete field": "Alanı sil", "Select collection": "Koleks<PERSON><PERSON> seçin", "Blank block": "Boş blok", "Duplicate template": "Şablonun kopyasını oluştur", "Reference template": "Referans <PERSON>", "Inherited template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Create calendar block": "<PERSON>k<PERSON><PERSON> blo<PERSON>", "Create kanban block": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "Grouping field": "<PERSON>", "Tab name": "Sekme adı", "Current record blocks": "Mevcut kayıt blokları", "Popup message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mesaj", "Delete role": "Rol sil", "Role display name": "Rol görünen adı", "Default role": "Varsayılan rol", "All collections use general action permissions by default; permission configured individually will override the default one.": "<PERSON><PERSON><PERSON>, varsay<PERSON>lan olarak genel eylem izinlerini kullanır; ayrı ayrı yapılandırılan izin, varsayılanı geçersiz kılar.", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "UI, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, izin<PERSON> vb. <PERSON><PERSON> olmak üzere tüm sistemin ya<PERSON>ılandırılmasına izin verir.", "New menu items are allowed to be accessed by default.": "<PERSON>ni menü öğelerine var<PERSON>ılan olarak erişilmesine izin verilir.", "Global permissions": "Sistem geneli izinler", "General permissions": "<PERSON><PERSON>", "Global action permissions": "Sistem geneli işlem izinleri", "General action permissions": "Genel işlem izinleri", "Action display name": "İşlem görünen adı", "Allow": "<PERSON><PERSON> ver", "Data scope": "<PERSON><PERSON> ka<PERSON>ı", "Action on new records": "Yeni kayıtlarda işlem", "Action on existing records": "Var olan kayıtlarda işlem", "All records": "<PERSON><PERSON><PERSON> ka<PERSON>", "Own records": "<PERSON><PERSON> ka<PERSON>", "Permission policy": "İzin politikası", "Individual": "<PERSON><PERSON><PERSON><PERSON>", "General": "<PERSON><PERSON>", "Accessible": "Erişilebilir", "Configure permission": "<PERSON><PERSON> yapılandırma", "Action permission": "İşlem yetkisi", "Field permission": "<PERSON>", "Scope name": "<PERSON><PERSON><PERSON> adı", "Unsaved changes": "Değişiklikler kaydedilmedi", "Are you sure you don't want to save?": "kaydetmek istemediğinizden emin misiniz??", "Dragging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Popup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Trigger workflow": "İş akışını tetikle", "Request API": "API İsteği", "Assign field values": "<PERSON> ata", "Constant value": "<PERSON><PERSON>", "Dynamic value": "<PERSON><PERSON><PERSON>", "Current user": "Seçili kullanıcı", "Current role": "Seçili rol", "Current record": "Se<PERSON><PERSON> ka<PERSON>ıt", "Current collection": "Seçili koleksiyon", "Other collections": "<PERSON><PERSON><PERSON>", "Current popup record": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere kaydı", "Parent popup record": "Üst açılır pencere kaydı", "Associated records": "İlişkili ka<PERSON>ı<PERSON>ar", "Parent record": "Üst kayıt", "Popup close method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere kapatma metodu", "Automatic close": "Otomatik kapat", "Manually close": "<PERSON>", "After successful update": "Başarılı güncellemeden sonra", "Save record": "Kaydı kaydet", "Updated successfully": "Başarıyla gü<PERSON>llendi", "After successful save": "Başarılı kaydetmeden sonra", "After clicking the custom button, the following field values will be assigned according to the following form.": "Özel butona tıklandıktan sonra aşağıdaki forma göre aşağıdaki alan değ<PERSON>leri atanacaktır.", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "Özel butona tıklandıktan sonra mevcut kaydın aşağıdaki alanları aşağıdaki forma göre kaydedilecektir.", "Button background color": "Buton arkaplan rengi", "Highlight": "Vurgulu", "Danger red": "Tehl<PERSON>li kırmızı", "Custom request": "<PERSON><PERSON> is<PERSON>k", "Request settings": "İstek ayarları", "Request URL": "İstek URL adresi", "Request method": "İstek türü", "Request query parameters": "İstek sorgusu parametreleri", "Request headers": "İstek başlıkları", "Request body": "İstek gövde", "Request success": "İstek başarılı", "Invalid JSON format": "Hatalı JSON formatı", "After successful request": "Başarılı istekten sonra", "Add exportable field": "Dışa aktarılabilir al<PERSON>", "Audit logs": "<PERSON><PERSON><PERSON>", "Record ID": "Kayıt ID", "User": "Kullanıcı", "Field": "<PERSON>", "Field value changes": "<PERSON>iklikleri", "One to one (has one)": "Bire-Bir (bir tane var)", "One to one (belongs to)": "Bire-Bir (ait)", "Use the same time zone (GMT) for all users": "Tüm kullanıcılar için aynı saat dilimini (GMT) kullanın", "Block title": "Blok başlığı", "Edit block title": "Blok başlığını düzenle", "operater": "operatör", "Province/city/area name": "Semt/şehir/bölge adı", "Field component": "<PERSON>", "Allow multiple": "Birden çok izin ver", "Quick upload": "Hızlı yükleme", "Select file": "<PERSON><PERSON><PERSON>", "Subtable": "Alttablo", "Subform": "Altform", "Regular expression": "Model(<PERSON><PERSON>)", "Enabled languages": "<PERSON><PERSON><PERSON><PERSON>", "View all plugins": "<PERSON><PERSON>m eklentileri g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Print": "Yazdır", "Single select and radio fields can be used as the grouping field": "Gruplama alanı olarak tek seçim ve radyo alanları kullanılabilir", "Sign up successfully, and automatically jump to the sign in page": "Başarılı bir şekilde kaydolun ve otomatik olarak oturum açma sayfasına geçin", "Search and select collection": "Koleksiyon ara ve seç", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "Bu, NocoBase iç işlevleri hatası olabilir. Lütfen <1>burada</1> bir sorun açın", "Render Failed": "Oluşturma başarısız", "Feedback": "<PERSON><PERSON>", "Try again": "<PERSON><PERSON><PERSON> dene", "Click or drag file to this area to upload": "Dosyayı yüklemek için buraya tıklayın veya sürükleyin", "Support for a single or bulk upload, file size should not exceed": "Tek veya toplu yü<PERSON><PERSON>, dosya boyutu a<PERSON>ı<PERSON>", "Default title for each record": "Her kayıt i<PERSON><PERSON> ba<PERSON><PERSON>k", "If collection inherits, choose inherited collections as templates": "Koleksiyon mi<PERSON> alı<PERSON>, kalıtılan koleksiyonları şablon olarak seçin", "Select an existing piece of data as the initialization data for the form": "Formun başlangıç ​​verileri olarak mevcut bir veri parça<PERSON>ını seçin", "Only the selected fields will be used as the initialization data for the form": "Yalnızca seçilen al<PERSON>, formun başlangıç ​​verileri olarak kullanılacaktır", "Template Data": "Şablon <PERSON>", "Data fields": "<PERSON><PERSON>", "Add template": "<PERSON><PERSON><PERSON>", "Display data template selector": "Veri şablonu seçicisini gö<PERSON>", "Form data templates": "Form veri şablonları", "Data template": "<PERSON><PERSON>", "New plugin": "<PERSON><PERSON>", "Upgrade": "Yükselt", "Dependencies check failed": "Bağımlılıkların kontrolü başarısız oldu", "More details": "<PERSON><PERSON> fazla detay", "Upload new version": "<PERSON><PERSON>", "Version": "S<PERSON>r<PERSON><PERSON>", "Npm package": "Npm paketi", "Npm package name": "Npm paket adı", "Upload plugin": "<PERSON><PERSON><PERSON>", "Official plugin": "<PERSON><PERSON><PERSON>", "Add type": "<PERSON><PERSON><PERSON>", "Changelog": "Değişiklik günlüğü", "Dependencies check": "Bağımlılıkların kontrolü", "Update plugin": "Eklentiyi yükselt", "Installing": "Yükleniyor", "The deletion was successful.": "<PERSON><PERSON><PERSON> b<PERSON>ılı.", "Plugin Zip File": "Eklenti Zip <PERSON>ı", "Compressed file url": "Sıkıştırılmış dosya bağlantısı", "Last updated": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>", "PackageName": "<PERSON><PERSON>", "DisplayName": "Görünen Ad", "Readme": "Okuma Dosyası", "Dependencies compatibility check": "Bağımlılık uyumluluğu kontrolü", "If the compatibility check fails, you should change the dependent version to meet the version requirements.": "Uyumluluk kontrolü başarısız olursa, bağımlı olan sürümü değiştirmeniz gerekmektedir.", "Version range": "<PERSON><PERSON><PERSON><PERSON>m a<PERSON>ığı", "Plugin's version": "<PERSON><PERSON><PERSON><PERSON>", "Result": "<PERSON><PERSON><PERSON>", "No CHANGELOG.md file": "CHANGELOG.md dosyası bulunmamaktadır", "No README.md file": "README.md dosyası bulunmamaktadır", "Homepage": "<PERSON><PERSON><PERSON>", "Drag and drop the file here or click to upload, file size should not exceed 30M": "Dosyayı buraya sürükleyin veya yüklemek için tı<PERSON>ın, dosya boyutu 30M'i geçmemelidir", "Dependencies check failed, can't enable.": "Bağımlılık kontrolü başarısız oldu, etkinleştirilemiyor.", "Plugin starting...": "Eklenti başlatılıyor...", "Plugin stopping...": "Eklenti durduruluyor...", "Are you sure to delete this plugin?": "Bu eklentiyi silmek istediğinizden emin misiniz?", "re-download file": "dosyayı yeniden indir", "Not enabled": "<PERSON><PERSON><PERSON>", "Search plugin": "Eklenti ara", "Author": "<PERSON><PERSON>", "Plugin loading failed. Please check the server logs.": "Eklenti yüklenemedi. Lütfen sunucu günlüklerini kontrol edin.", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name is required": "ad gereklidir", "data source": "veri ka<PERSON>", "Data source": "veri ka<PERSON>", "DataSource": "Veri <PERSON>ı", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "{{type}} \"{{name}}\" silinmiş olabilir. Lütfen bu {{blockType}}'yi kaldı<PERSON>.", "Home page": "<PERSON><PERSON><PERSON>", "Handbook": "Kullanıcı kılavuzu", "License": "Lisa<PERSON>", "This variable has been deprecated and can be replaced with \"Current form\"": "Değişken kullanımdan kaldırıldı; \"Geçerli form\" yerine kullanılabilir", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "<PERSON>u değişkenin değeri sayfa URL'sinin sorgu dizgisinden türetilir. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, sayfanın bir sorgu dizgisi olduğunda yalnızca normal olarak kullanılabilir.", "URL search params": "URL arama parametreleri", "Expand All": "Tümünü genişlet", "Clear default value": "Varsayılan değeri temizle", "Open in new window": "<PERSON><PERSON> pencerede aç", "Sorry, the page you visited does not exist.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON> ettiğiniz sayfa mevcut değil.", "Allow multiple selection": "Çoklu seçim izni", "Parent object": "<PERSON><PERSON> ne<PERSON>ne", "Ellipsis overflow content": "Üç nokta ile taşan içerik", "Hide column": "<PERSON><PERSON><PERSON><PERSON> gizle", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "Ya<PERSON><PERSON><PERSON><PERSON>rma modunda, tüm sütun tamamen saydamlık alır. Yapılandırma modu olmayan durumda, tüm sütun gizlenir. Tamamen sütun gizlendiğinde bile, ya<PERSON><PERSON><PERSON><PERSON>r<PERSON>lm<PERSON>ş varsayılan değerleri ve diğer ayarları hâlâ etkin olur.", "Desktop routes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Route permissions": "Rota <PERSON>i", "New routes are allowed to be accessed by default": "<PERSON><PERSON> var<PERSON>ılan olarak erişilebilir", "Route name": "Rota adı", "Mobile routes": "<PERSON><PERSON>", "Show in menu": "<PERSON><PERSON><PERSON>", "Hide in menu": "<PERSON><PERSON><PERSON> g<PERSON>", "Path": "Yol", "Type": "Tip", "Access": "<PERSON><PERSON><PERSON><PERSON>", "Routes": "R<PERSON>lar", "Add child route": "Alt rota ekle", "Delete routes": "Rotaları sil", "Delete route": "Rota sil", "Are you sure you want to hide these routes in menu?": "Bu rotaları menüde gizlemek istediğinizden emin misiniz?", "Are you sure you want to show these routes in menu?": "Bu rotaları menüde göstermek istediğinizden emin misiniz?", "Are you sure you want to hide this menu?": "Bu menüyü gizlemek istediğinizden emin misiniz?", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "G<PERSON><PERSON><PERSON><PERSON><PERSON> sonra, bu menü artık menü çubuğunda görünmeyecektir. Tekrar görüntülemek için, yönlendirme yönetimi sayfasına gidip onu yapılandırmanız gerekecektir.", "If selected, the page will display Tab pages.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fa Tab sayfalarını görüntüleyecektir.", "If selected, the route will be displayed in the menu.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yol menüde görüntülenecektir.", "Are you sure you want to hide this tab?": "Bu sekmeyi gizlemek istediğinizden emin misiniz?", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "G<PERSON><PERSON>dikten sonra, bu sekme artık sekme çubuğunda görünmeyecek. Onu tekrar göstermek için, rotayı yönetim say<PERSON>ına gidip a<PERSON>lamanız gerekiyor.", "Deprecated": "Kullanımdan kaldırıldı", "Full permissions": "<PERSON><PERSON><PERSON>", "Refresh data blocks": "<PERSON><PERSON><PERSON> veri blokları", "Select data blocks to refresh": "Veri bloklarını yenilemek için seçin", "After successful submission, the selected data blocks will be automatically refreshed.": "Başarılı bir şekilde gönderildikten sonra, seçilen veri blokları otomatik olarak yenilenecektir.", "No pages yet, please configure first": "<PERSON><PERSON><PERSON>z <PERSON> yok, lütfen önce yapılandırın", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "Kullanıcı arayüzü düzenleyici moduna girmek için sağ üst köşedeki \"Kullanıcı Arayüzü Düzenleyici\" simgesine tıklayın", "Reset link expiration": "Bağlantı süresini sıfırla"}