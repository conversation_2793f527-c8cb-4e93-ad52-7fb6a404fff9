{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "Показать <1><0>10</0><1>20</1><2>50</2><3>100</3></1> элементов на странице", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "Выполнить <1><0>Все</0><1>Любое</1></1> условия в группе", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "Открыть в<1><0>Модальное окно</0><1>Я<PERSON>ик</1><2>Окно</2></1>", "{{count}} filter items": "{{count}} отфильтровано элементов", "{{count}} more items": "{{count}} больше элементов", "Total {{count}} items": "Всего {{count}} элементов", "Today": "Сегодня", "Yesterday": "Вчера", "Tomorrow": "Завтра", "Month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Week": "Неделя", "This week": "Эта неделя", "Next week": "Следующая неделя", "This month": "Этот месяц", "Next month": "Следующий месяц", "Last quarter": "Прошлый квартал", "This quarter": "Этот квартал", "Next quarter": "Следующий квартал", "This year": "Этот год", "Next year": "Следующий год", "Last week": "Прошлая неделя", "Last month": "Прошлый месяц", "Last year": "Прошлый год", "Last 7 days": "Последние 7 дней", "Last 30 days": "Последние 30 дней", "Last 90 days": "Последние 90 дней", "Next 7 days": "Следующие 7 дней", "Next 30 days": "Следующие 30 дней", "Next 90 days": "Следующие 90 дней", "Work week": "Рабочая неделя", "Day": "День", "Agenda": "Расписание", "Date": "Дата", "Time": "Время", "Event": "Событие", "None": "Ничего", "Unconnected": "Не подключен", "System settings": "Системные настройки", "System title": "Системный заголовок", "Logo": "Лого<PERSON>ип", "Add menu item": "Добавить элемент меню", "Page": "Страница", "Tab": "Таб", "Name": "Имя", "Icon": "Иконка", "Group": "Группа", "Link": "Ссылка", "Save conditions": "Сохранить условия", "Edit menu item": "Изменить пункт меню", "Move to": "Перенести", "Insert left": "Вставить слева", "Insert right": "Вставить справа", "Insert inner": "Вставить внутрь", "Delete": "Удалить", "Disassociate": "Разъединить", "Disassociate record": "Разъединить запись", "Are you sure you want to disassociate it?": "Вы уверены, что хотите разъединить это?", "UI editor": "UI редактор", "Collection": "Коллекция", "Collections & Fields": "Коллекции & Поля", "Roles & Permissions": "Роли & Разрешения", "Edit profile": "Изменить профиль", "Change password": "Сменить пароль", "Old password": "Старый пароль", "New password": "Новый пароль", "Switch role": "Сменить роль", "Super admin": "Супер админ", "Language": "Язык", "Allow sign up": "Разрешить регистрацию", "Sign out": "Выйти", "Cancel": "Отмена", "Submit": "Отправить", "Close": "Закрыть", "Set the data scope": "Установить область данных", "Data loading mode": "Режим загрузки данных", "Set data loading mode": "Установить режим загрузки данных", "Load all data when filter is empty": "Загружать все данные, когда фильтр пуст", "Do not load data when filter is empty": "Не загружать данные, когда фильтр пуст", "Data blocks": "Блоки данных", "Filter blocks": "Просеивающие блоки", "Table": "Таблица", "Form": "Форма", "Collapse": "Свернуть", "Select data source": "Выбрать источник данных", "Calendar": "Календарь", "Kanban": "Ка<PERSON><PERSON><PERSON>н", "Select grouping field": "Выбрать поле группировки", "Media": "Медиа", "Markdown": "<PERSON><PERSON>", "Wysiwyg": "Wysiwyg", "Chart blocks": "Блоки диаграммы", "Column chart": "Колонки", "Bar chart": "Столбики", "Line chart": "Линейная", "Pie chart": "Круговая", "Area chart": "Область", "Other chart": "Другая диаграмма", "Other blocks": "Другие блоки", "In configuration": "В конфигурации", "Chart title": "Заголовок диаграммы", "Chart type": "Тип диаграммы", "Chart config": "Конфиг. диаграммы", "Templates": "Шаблоны", "Template": "Шабл<PERSON>н", "Select template": "Выбрать шаблон", "Action logs": "Журналы действий", "Create template": "Создать шаблон", "Edit markdown": "Редактировать markdown", "Add block": "Добавить блок", "Add new": "Добавить новый", "Add record": "Зобавить запись", "Custom field display name": "Отображаемое имя поля пользователя", "Display fields": "Показать поля", "Edit record": "Изменить запись", "Delete menu item": "Удалить пункт меню", "Add page": "Добавить страницу", "Add group": "Добавить группу", "Add link": "Добавить ссылку", "Insert above": "Вставить выше", "Insert below": "Вставить ниже", "Save": "Сохранить", "Delete block": "Удалить блок", "Are you sure you want to delete it?": "Вы уверены, что хотите удалить это?", "This is a demo text, **supports Markdown syntax**.": "Это демо текст, **поддерживает синтаксис Markdown**.", "Filter": "Фильтр", "Connect data blocks": "Соединить блоки данных", "Action type": "Тип действия", "Actions": "Действия", "Insert": "Вставить", "Update": "Обновить", "View": "Вид", "View record": "Показать запись", "Refresh": "Обновить", "Data changes": "Изменения данных", "Field name": "Имя поля", "Before change": "До изменений", "After change": "После изменений", "Delete record": "Удалить запись", "Create collection": "Создать Коллекцию", "Collection display name": "Отображение имени Коллекции", "Collection name": "Имя Коллекции", "Categories": "Категории таблиц данных", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.": "Случайно сгенерированный и может быть изменен. Поддерживает буквы, цифры и подчеркивания, должно начинаться с буквы.", "Edit": "Изменить", "Edit collection": "Изменить Коллекцию", "Configure fields": "Конфигурировать поля", "Configure columns": "Конфигурировать колонки", "Edit field": "Изменить поле", "Configure fields of {{title}}": "Конфигурировать поле {{title}}", "Basic": "Базовый", "Single line text": "Текстовая строка", "Long text": "Текстовое поле", "Phone": "Телефон", "Email": "Email", "Number": "Число", "Integer": "Целое", "Percent": "Процент", "Password": "Пароль", "Advanced type": "Продвинутый", "Formula": "Формула", "Formula description": "Вычисляет значение на базе значений других полей той же записи.", "Choices": "Выбор", "Checkbox": "Чекбокс", "Single select": "Одиночный выбор", "Multiple select": "Множественный выбор", "Radio group": "Радио группа", "Checkbox group": "Чекбокс группа", "China region": "Китай регион", "Date & Time": "Дата & Время", "Datetime": "Датавремя", "Relation": "Связь", "Link to": "Ссылка на", "Link to description": "Используется для быстрого создания связей коллекций и совместим с наиболее распространенными сценариями. Подходит для использования не разработчиками. Когда он присутствует в виде поля, это раскрывающийся список, используемый для выбора записей из целевой коллекции. После создания он одновременно генерирует связанные поля текущей коллекции в целевой коллекции.", "Sub-table": "Подтаблица", "System info": "Системная инфо", "Created at": "Когда создано", "Last updated at": "Когда изменено", "Created by": "Кто создал", "Last updated by": "Кто изменил", "Add field": "Добавить поле", "Field display name": "Отображаемое имя поля", "Field type": "Тип поля", "Field interface": "Интерфейс поля", "Date format": "Формат даты", "Year/Month/Day": "Год/Месяц/День", "Year-Month-Day": "Год-Месяц-День", "Day/Month/Year": "День/Месяц/Год", "Show time": "Показать время", "Time format": "Фрмат времени", "12 hour": "12 час", "24 hour": "24 час", "Relationship type": "Тип отношений", "Source collection": "Исходная коллекция", "Source key": "Исходный ключ", "Target collection": "Целевая коллекция", "Through collection": "Че<PERSON><PERSON>з коллекцию", "Target key": "Целевой ключ", "Foreign key": "Внеш<PERSON><PERSON> ключ", "One to one": "Один к одному", "One to many": "Один ко многим", "Many to one": "Многие к одному", "Many to many": "Многие ко многим", "One to one description": "Используется для создания отношений один к одному. Например, у пользователя есть профиль.", "One to many description": "Используется для создания отношения «один ко многим». Например, в стране может быть много городов, а город может находиться только в одной стране. Когда он присутствует в виде поля, это подтаблица, в которой отображаются записи связанной коллекции. При создании поле «многие к одному» автоматически создается в связанной коллекции.", "Many to one description": "Используется для создания отношений «многие к одному». Например, город может принадлежать только одной стране, а в стране может быть много городов. Когда он присутствует в виде поля, это раскрывающийся список, используемый для выбора записи из связанной коллекции. После создания поле «один ко многим» автоматически создается в связанной коллекции.", "Many to many description": "Используется для создания отношений «многие ко многим». Например, у ученика будет много учителей, а у учителя будет много учеников. Когда он присутствует в виде поля, это раскрывающийся список, используемый для выбора записей из связанной коллекции.", "Foreign key 1": "Внешний ключ 1", "Foreign key 2": "Внешний ключ 2", "Add filter": "Добавить фильтр", "Add filter group": "Добавить группу фильтров", "Comparision": "Сравнение", "is": "соответствует", "is not": "не соответствует", "contains": "содер<PERSON><PERSON>т", "does not contain": "не содержит", "starts with": "начинается с", "not starts with": "не начинается с", "ends with": "оканчивается на", "not ends with": "не оканчивается на", "is empty": "пустое", "is not empty": "не пустое", "Edit chart": "Изменить диаграмму", "Add text": "Добавить текст", "Filterable fields": "Фильтруемые поля", "Edit button": "Изменить кнопку", "Hide": "Скрыть", "Enable actions": "Разрешить действия", "Export": "Экспортировать", "Customize": "Настроить", "Function": "Функция", "Popup form": "Всплывающая форма", "Flexible popup": "Гибкое всплывающее окно", "Configure actions": "Настройка действий", "Display order number": "Показать порядковые номера", "Enable drag and drop sorting": "Разрешить сортировку перетаскиванием", "Triggered when the row is clicked": "Срабатывает при нажатии на строку", "Add tab": "Добавить вкладку", "Disable tabs": "Запретить вкладки", "Details": "Подробности", "Edit form": "Изменить форму", "Create form": "Создать форму", "Form (Edit)": "Форма (Изменить)", "Form (Add new)": "Форма (Добавить новый)", "Edit tab": "Изменить вкладку", "Relationship blocks": "Блоки отношений", "Select record": "Выбрать запись", "Display name": "Показать имя", "Select icon": "Выбрать иконку", "Custom column name": "Пользовательское имя колонки", "Edit description": "Изменить описание", "Required": "Обязательное", "Label field": "Метка поля", "Default is the ID field": "По умолчанию это поле ID", "Set default sorting rules": "Установить правила сортировки по умолчанию", "is before": "находится до", "is after": "находится после", "is on or after": "находится на или после", "is on or before": "находится на или до", "is between": "находится в диапазоне", "Upload": "Закачать", "Select level": "Выберите уровень", "Province": "Область", "City": "Город", "Area": "Рай<PERSON>н", "Street": "Улица", "Village": "Деревня", "Must select to the last level": "Нужно выбрать до последнего уровня", "Move {{title}} to": "Переместить {{title}} на", "Target position": "Целевая позиция", "After": "После", "Before": "До", "Add {{type}} before \"{{title}}\"": "Добавить {{type}} до \"{{title}}\"", "Add {{type}} after \"{{title}}\"": "Добавить {{type}} после \"{{title}}\"", "Add {{type}} in \"{{title}}\"": "Добавить {{type}} в \"{{title}}\"", "Original name": "Оригинальное имя", "Custom name": "Пользовательское имя", "Custom Title": "Пользовательский Заголовок", "Options": "Опции", "Option value": "Значение опции", "Option label": "Метка опции", "Color": "Цвет", "Add option": "Добавить опцию", "Related collection": "Связанная коллекция", "Allow linking to multiple records": "Позволить прилинковать много записей", "Configure calendar": "Настроить календарь", "Title field": "Поле заголовка", "Start date field": "Поле даты начала", "End date field": "Поле даты окончания", "Navigate": "Навигация", "Title": "Заголовок", "Description": "Описание", "Select view": "Выбрать вид", "Reset": "Сбросить", "Exportable fields": "Экспортируемые поля", "Saved successfully": "Успешно сохранено", "Nickname": "Никнейм", "Sign in": "Войти", "Create an account": "Создать аккаунт", "Sign up": "Зарегистрироваться", "Confirm password": "Подтвердить пароль", "Log in with an existing account": "Войти в существующий аккаунт", "Signed up successfully. It will jump to the login page.": "Успешно зарегистрировано. Вы будете перенаправлены на страницу входа.", "Password mismatch": "Пароли не совпадают", "Users": "Пользователи", "Roles": "Роли", "Add role": "Добавить роль", "Role name": "Имя роли", "Configure": "Настроить", "Configure permissions": "Настроить разрешения", "Edit role": "Изменить роль", "Action permissions": "Разрешения на действия", "Menu permissions": "Разрешения на Меню", "Menu item name": "Название пункта Меню", "Allow access": "Разрешить доступ", "Action name": "Название действия", "Allow action": "Разрешить действия", "Action scope": "Область действий", "Operate on new data": "Работать с новыми данными", "Operate on existing data": "Работать с существующими данными", "Yes": "Да", "No": "Нет", "Red": "Красный", "Magenta": "Сиреневый", "Volcano": "Вулканический", "Orange": "Оранжевый", "Gold": "Золотой", "Lime": "<PERSON><PERSON><PERSON><PERSON>", "Green": "Зеленый", "Cyan": "Го<PERSON><PERSON><PERSON><PERSON><PERSON>", "Blue": "Синий", "Geek blue": "Тёмно-синий", "Purple": "Фиолетовый", "Default": "По умолчанию", "Add card": "Добавить карточку", "edit title": "изменить заголовок", "Turn pages": "Перелистывать страницы", "Others": "Другие", "Other records": "Другие записи", "Save as reference template": "Сохранить как шаблон ссылки", "Save as inherited template": "Сохранить как шаблон наследования", "Save as block template": "Сохранить как шаблон Блока", "Block templates": "Шаблоны блоков", "Convert reference to duplicate": "Преобразовать ссылку в дубликат", "Template name": "Имя Шаблона", "Block type": "Тип Бло<PERSON>а", "No blocks to connect": "Нет Блоков для подключения", "Action column": "Колонка действий", "Records per page": "Записей на страницу", "(Fields only)": "(Только поля)", "Button title": "Имя кнопки", "Button icon": "Иконка кнопки", "Submitted successfully": "Успешно отправлено", "Operation succeeded": "Операция прошла успешно", "Operation failed": "Операция не удалась", "Open mode": "Открытый режим", "Menu item title": "Заголовок пункта Меню", "Menu item icon": "Иконка пункта Меню", "Target": "Цель", "Position": "Положение", "Insert before": "Вставить до", "Insert after": "Вставить после", "UI Editor": "UI Редактор", "ASC": "по возр.", "DESC": "по убыв.", "Add sort field": "Добавить поле сортировки", "ID": "ID", "Drawer": "Я<PERSON><PERSON><PERSON>", "Dialog": "Ди<PERSON><PERSON><PERSON><PERSON>", "Delete action": "Удалить действие", "Custom column title": "Пользовательский заголовок колонки", "Column title": "заголовок столбца", "Original title: ": "Заголовок по умолчанию: ", "Delete table column": "Удалить колонку таблицы", "Skip required validation": "Пропустить обязатеьную проверку", "Form values": "Значения формы", "Fields values": "Значения полей", "The field has been deleted": "Поле было удалено", "When submitting the following fields, the saved values are": "При отправке следующих полей, сохраненные значения такие", "After successful submission": "После удачной отправки", "Then": "Затем", "Stay on current page": "Оставаться на текущей странице", "Redirect to": "Перенаправить на", "Save action": "Сохранить действие", "Exists": "Существуют", "Add condition": "Добавить правило", "Add condition group": "Добавить группу правил", "exists": "существуют", "not exists": "не существуют", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "UID Роли", "Precision": "Точность", "Formula mode": "Режим формулы", "Expression": "Переменная", "Input +, -, *, /, ( ) to calculate, input @ to open field variables.": "Введите +, -, *, /, ( ) для вычисления, введите @ чтобы открыть переменные поля.", "Formula error.": "Ошибка формулы.", "Rich Text": "Rich Text", "Junction collection": "Коллекция Узлов", "Leave it blank, unless you need a custom intermediate table": "Оставьте это поле пустым, если вам не нужна пользовательская промежуточная таблица.", "Fields": "Поля", "Edit field title": "Изменить заголовок поля", "Field title": "Заголовок поля", "Original field title: ": "Оригинальный заголовок поля: ", "Edit tooltip": "Изменить подсказку", "Delete field": "Удалить поле", "Select collection": "Выбрать коллекцию", "Blank block": "Пустой блок", "Duplicate template": "Дублировать шаблон", "Reference template": "Справочный шаблон", "Inherited template": "Наследуемый шаблон", "Create calendar block": "Создать блок календаря", "Create kanban block": "Создать блок Канбан", "Grouping field": "Поле группировки", "Tab name": "Имя вкладки", "Current record blocks": "Блоки текущей записи", "Popup message": "Всплывающее сообщение", "Delete role": "Удалить роль", "Role display name": "Имя роли на экране", "Default role": "Роль по умолчанию", "All collections use general action permissions by default; permission configured individually will override the default one.": "Все коллекции по умолчанию используют общие права доступа; разрешение, настроенное индивидуально, переопределит разрешение по умолчанию.", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "Позволяет настраивать всю систему, включая пользовательский интерфейс, коллекции, разрешения и т. д.", "New menu items are allowed to be accessed by default.": "Доступ к новым пунктам меню разрешен по умолчанию.", "Global permissions": "Глобальные разрешения", "General permissions": "Общие разрешения", "Global action permissions": "Глобальные разрешения на действия", "General action permissions": "Общие разрешения на действия", "Action display name": "Имя действия на экране", "Allow": "Разрешить", "Data scope": "Облась данных", "Action on new records": "Действие на новых записях", "Action on existing records": "Действие на существующих записях", "All records": "Все записи", "Own records": "собственные записи", "Permission policy": "Политика разрешений", "Individual": "Индивидуально", "General": "Общие", "Accessible": "Доступно", "Configure permission": "Настроить разрешения", "Action permission": "Разрешения на действия", "Field permission": "Разрешения на поля", "Scope name": "Имя области", "Unsaved changes": "Несохраненные изменения", "Are you sure you don't want to save?": "Вы уверены, что не хотите сохранить?", "Dragging": "Перетаскивание", "Popup": "Всплывающий", "Trigger workflow": "Рабочий процесс Триггера", "Request API": "Запрос API", "Assign field values": "Присвоить значения поля", "Constant value": "Постоянное значение", "Dynamic value": "Динамическое значение", "Current user": "Текущий пользователь", "Current role": "Текущая роль", "Current record": "Текущая запись", "Current collection": "Текущая коллекция", "Other collections": "Другие коллекции", "Current popup record": "Текущая запись всплывающего окна", "Parent popup record": "Родительская запись всплывающего окна", "Associated records": "Связанные записи", "Parent record": "Родительская запись", "Popup close method": "Метод закрытия всплывающего окна", "Automatic close": "Автоматическое закрытие", "Manually close": "Ручное закрытие", "After successful update": "После успешного обновления", "Save record": "Сохранить запись", "Updated successfully": "Успешно обновлено", "After successful save": "После успешного сохранения", "After clicking the custom button, the following field values will be assigned according to the following form.": "После нажатия пользовательской кнопки следующие значения полей будут назначены в соответствии со следующей формой.", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "После нажатия пользовательской кнопки следующие поля текущей записи будут сохранены в соответствии со следующей формой.", "Button background color": "Цвет фона кнопки", "Highlight": "Подсветка", "Danger red": "Опасно красный", "Custom request": "Пользовательский запрос", "Request settings": "Настройки запроса", "Request URL": "URL запроса", "Request method": "Метод запроса", "Request query parameters": "Запрос параметров запроса", "Request headers": "Заголовки запроса", "Request body": "Тело запроса", "Request success": "Успешный запрос", "Invalid JSON format": "Неверный JSON формат", "After successful request": "После успешного запроса", "Add exportable field": "Добавить экспортируемое поле", "Audit logs": "Журна<PERSON>ы аудита", "Record ID": "ID записи", "User": "Пользователь", "Field": "Поле", "Field value changes": "Изменения значения поля", "One to one (has one)": "Один к одному (has one)", "One to one (belongs to)": "Один к одному (belongs to)", "Use the same time zone (GMT) for all users": "Использовать одну и ту же временную зону (GMT) для всех пользователей", "Block title": "Заголовок блока", "Edit block title": "Изменить заголовок блока", "Province/city/area name": "Имя области/города/района", "Field component": "Компонент поля", "Allow multiple": "Разрешить множественный выбор", "Quick upload": "Быстрая загрузка", "Select file": "Выбрать файл", "Subtable": "Подтаблица", "Sub-form": "Подформа", "Regular expression": "Обр<PERSON><PERSON><PERSON><PERSON>", "Enabled languages": "Включенные языки", "View all plugins": "Посмотреть все плагины", "Print": "Печать", "Single select and radio fields can be used as the grouping field": "Одиночное поле выбора и радиополя могут использоваться в качестве поля группировки", "Sign up successfully, and automatically jump to the sign in page": "Зарегистрируйтесь успешно и автоматически перейдете на страницу входа", "Search and select collection": "Поиск и выбор коллекции", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "Это, вероятно, ошибка внутреннего устройства NocoBase. Пожалуйста, откройте проблему <1>здесь</1>", "Render Failed": "Ошибка рендеринга", "Feedback": "Обратная связь", "Try again": "Попробуйте еще раз", "Click or drag file to this area to upload": "Нажмите или перетащите файл в эту область, чтобы загрузить", "Support for a single or bulk upload, file size should not exceed": "Поддержка одиночной или массовой загрузки, размер файла не должен превышать", "Default title for each record": "Заголовок по умолчанию для каждой записи", "If collection inherits, choose inherited collections as templates": "Если коллекция наследуется, выберите наследуемые коллекции в качестве шаблонов", "Select an existing piece of data as the initialization data for the form": "Выберите существующие данные в качестве исходных данных для формы", "Only the selected fields will be used as the initialization data for the form": "Только выбранные поля будут использоваться в качестве исходных данных для формы", "Template Data": "Шаблон данных", "Data fields": "Поля данных", "Add template": "Добавить шаблон", "Display data template selector": "Отображать селектор шаблона данных", "Form data templates": "Шаблоны данных формы", "Data template": "Шаблон данных", "Not found": "Не найдено", "Add": "Добавить", "Select all": "Выбрать все", "New plugin": "Новый плагин", "Upgrade": "Обновить", "Dependencies check failed": "Проверка зависимостей не удалась", "More details": "Подробнее", "Upload new version": "Загрузить новую версию", "Version": "Версия", "Npm package": "Npm пакет", "Npm package name": "Имя npm пакета", "Upload plugin": "Загрузить плагин", "Official plugin": "Официальный плагин", "Add type": "Добавить тип", "Changelog": "Изменения", "Dependencies check": "Проверка зависимостей", "Update plugin": "Обновить плагин", "Installing": "Установка", "The deletion was successful.": "Удаление прошло успешно.", "Plugin Zip File": "Zip файл плагина", "Compressed file url": "URL сжатого файла", "Last updated": "Последнее обновление", "PackageName": "Имя пакета", "DisplayName": "Отображаемое имя", "Readme": "Инструкция", "Dependencies compatibility check": "Проверка совместимости зависимостей", "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.": "Если проверка совместимости зависимостей не удалась, вы должны изменить версию зависимости, чтобы соответствовать требованиям версии.", "Version range": "Диапазон версий", "Plugin's version": "Версия плагина", "Result": "Результат", "No CHANGELOG.md file": "Нет файла CHANGELOG.md", "No README.md file": "Нет файла README.md", "Homepage": "Домашняя страница", "Drag and drop the file here or click to upload, file size should not exceed 30M": "Перетащите файл сюда или нажмите, чтобы загрузить, размер файла не должен превышать 30M", "Dependencies check failed, can't enable.": "Проверка зависимостей не удалась, невозможно включить.", "Plugin starting...": "Запуск плагина...", "Plugin stopping...": "Остановка плагина...", "Are you sure to delete this plugin?": "Вы уверены, что хотите удалить этот плагин?", "re-download file": "повторно загрузить файл", "Not enabled": "Не включено", "Search plugin": "Поиск плагина", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin loading failed. Please check the server logs.": "Не удалось загрузить плагин. Пожалуйста, проверьте журналы сервера.", "loading": "загрузка", "name is required": "имя обязательно", "data source": "источник данных", "Data source": "источник данных", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "{{type}} \"{{name}}\" возможно был удален. Пожалуйста, удалите этот {{blockType}}.", "DataSource": "Источник данных", "Home page": "Домашняя страница", "Handbook": "Руководство пользователя", "License": "Лицензия", "This variable has been deprecated and can be replaced with \"Current form\"": "Переменная устарела; \"Текущая форма\" может быть использована в качестве замены", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "Значение этой переменной происходит из строки запроса URL страницы. Эта переменная может использоваться только в том случае, если у страницы есть строка запроса.", "URL search params": "Параметры поиска URL", "Expand All": "Развернуть все", "Clear default value": "Очистить значение по умолчанию", "Open in new window": "Открыть в новом окне", "Sorry, the page you visited does not exist.": "Извините, посещенной вами страницы не существует.", "Allow multiple selection": "Разрешить множественный выбор", "Parent object": "Родительский объект", "Ellipsis overflow content": "Содержимое с многоточием при переполнении", "Hide column": "Скрыть столбец", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "В режиме конфигурации вся колонка становится прозрачной. В режиме не конфигурации вся колонка будет скрыта. Даже если вся колонка будет скрыта, её настроенные значения по умолчанию и другие настройки все равно будут действовать.", "Desktop routes": "Маршруты рабочего стола", "Route permissions": "Разрешения маршрутов", "New routes are allowed to be accessed by default": "Новые маршруты разрешены для доступа по умолчанию", "Route name": "Название маршрута", "Mobile routes": "Маршруты мобильных устройств", "Show in menu": "Показать в меню", "Hide in menu": "Скрыть в меню", "Path": "Путь", "Type": "Тип", "Access": "Доступ", "Routes": "Мар<PERSON><PERSON>уты", "Add child route": "Добавить дочерний маршрут", "Delete routes": "Удалить маршруты", "Delete route": "Удалить маршрут", "Are you sure you want to hide these routes in menu?": "Вы уверены, что хотите скрыть эти маршруты в меню?", "Are you sure you want to show these routes in menu?": "Вы уверены, что хотите показать эти маршруты в меню?", "Are you sure you want to hide this menu?": "Вы уверены, что хотите скрыть это меню?", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "После скрытия этого меню он больше не будет отображаться в меню. Чтобы снова отобразить его, вам нужно будет перейти на страницу управления маршрутами и настроить его.", "If selected, the page will display Tab pages.": "Если выбран, страница будет отображать страницы с вкладками.", "If selected, the route will be displayed in the menu.": "Если выбран, маршрут будет отображаться в меню.", "Are you sure you want to hide this tab?": "Вы уверены, что хотите скрыть эту вкладку?", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "После скрытия этой вкладки она больше не будет отображаться во вкладке. Чтобы снова отобразить ее, вам нужно будет перейти на страницу управления маршрутами, чтобы установить ее.", "Deprecated": "Устаревший", "Full permissions": "Полные права", "Refresh data blocks": "Обновить блоки данных", "Select data blocks to refresh": "Выберите блоки данных для обновления", "After successful submission, the selected data blocks will be automatically refreshed.": "После успешной отправки выбранные блоки данных будут автоматически обновлены.", "No pages yet, please configure first": "Пока нет страниц, пожалуйста, настройте сначала", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "Нажмите на значок \"Редактор пользовательского интерфейса\" в правом верхнем углу, чтобы войти в режим редактора пользовательского интерфейса", "Reset link expiration": "Сбросить срок действия ссылки"}