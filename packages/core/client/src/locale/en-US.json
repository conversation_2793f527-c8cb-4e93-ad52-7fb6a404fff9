{"Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page": "Display <1><0>10</0><1>20</1><2>50</2><3>100</3></1> items per page", "Meet <1><0>All</0><1>Any</1></1> conditions in the group": "Meet <1><0>All</0><1>Any</1></1> conditions in the group", "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>": "Open in<1><0>Modal</0><1>Drawer</1><2>Window</2></1>", "{{count}} filter items": "{{count}} filter items", "{{count}} more items": "{{count}} more items", "Total {{count}} items": "Total {{count}} items", "Today": "Today", "Yesterday": "Yesterday", "Tomorrow": "Tomorrow", "Month": "Month", "Week": "Week", "This week": "This week", "This month": "This month", "This year": "This year", "Next year": "Next year", "Last week": "Last week", "Next week": "Next week", "Last month": "Last month", "Next month": "Next month", "Last quarter": "Last quarter", "This quarter": "This quarter", "Next quarter": "Next quarter", "Last year": "Last year", "Last 7 days": "Last 7 days", "Last 30 days": "Last 30 days", "Last 90 days": "Last 90 days", "Next 7 days": "Next 7 days", "Next 30 days": "Next 30 days", "Next 90 days": "Next 90 days", "Work week": "Work week", "Day": "Day", "Agenda": "Agenda", "Date": "Date", "Time": "Time", "Event": "Event", "None": "None", "Unconnected": "Unconnected", "System settings": "System settings", "System title": "System title", "Settings": "Settings", "Logo": "Logo", "Add menu item": "Add menu item", "Page": "Page", "Name": "Name", "Icon": "Icon", "Group": "Group", "Link": "Link", "Tab": "Tab", "Save conditions": "Save conditions", "Edit menu item": "Edit menu item", "Move to": "Move to", "Insert left": "<PERSON><PERSON><PERSON> left", "Insert right": "Insert right", "Insert inner": "Insert inner", "Delete": "Delete", "Disassociate": "Disassociate", "Disassociate record": "Disassociate record", "Are you sure you want to disassociate it?": "Are you sure you want to disassociate it?", "UI editor": "UI editor", "Collection": "Collection", "Collection selector": "Collection selector", "Providing certain collections as options for users, typically used in polymorphic or inheritance scenarios": "Providing certain collections as options for users, typically used in polymorphic or inheritance scenarios", "Collections & Fields": "Collections & Fields", "All collections": "All collections", "Add category": "Add category", "Enable child collections": "Enable child collections", "Allow adding records to the current collection": "Allow adding records to the current collection", "Delete category": "Delete category", "Edit category": "Edit category", "Collection category": "Collection category", "Collection template": "Collection template", "Sort": "Sort", "Categories": "Categories", "Visible": "Visible", "Read only": "Read only", "Easy reading": "Easy reading", "Hidden": "Hidden", "Hidden(reserved value)": "Hidden(reserved value)", "Not required": "Not required", "Value": "Value", "Disabled": "Disabled", "Enabled": "Enabled", "Problematic": "Problematic", "Setting": "Setting", "On": "On", "Off": "Off", "Empty": "Empty", "Linkage rule": "Linkage rule", "Linkage rules": "Linkage rules", "Condition": "Condition", "Properties": "Properties", "Add linkage rule": "Add linkage rule", "Add property": "Add property", "Category name": "Category name", "Roles & Permissions": "Roles & Permissions", "Edit profile": "Edit profile", "Change password": "Change password", "Old password": "Old password", "New password": "New password", "Switch role": "Switch role", "Super admin": "Super admin", "Language": "Language", "Allow sign up": "Allow sign up", "Enable SMS authentication": "Enable SMS authentication", "Sign out": "Sign out", "Cancel": "Cancel", "Submit": "Submit", "Close": "Close", "Set the data scope": "Set the data scope", "Set data loading mode": "Set data loading mode", "Load all data when filter is empty": "Load all data when filter is empty", "Do not load data when filter is empty": "Do not load data when filter is empty", "Data loading mode": "Data loading mode", "Data blocks": "Data blocks", "Filter blocks": "Filter blocks", "Table": "Table", "Table OID(Inheritance)": "Table OID(Inheritance)", "Form": "Form", "List": "List", "Grid Card": "Grid Card", "pixels": "pixels", "Screen size": "Screen size", "Display title": "Display title", "Set the count of columns displayed in a row": "Set the count of columns displayed in a row", "Column": "Column", "Phone device": "Phone device", "Tablet device": "Tablet device", "Desktop device": "Desktop device", "Large screen device": "Large screen device", "Collapse": "Collapse", "Select data source": "Select data source", "Calendar": "Calendar", "Delete events": "Delete events", "This event": "This event", "This and following events": "This and following events", "All events": "All events", "Delete this event?": "Delete this event?", "Delete Event": "Delete Event", "Kanban": "Ka<PERSON><PERSON>", "Gantt": "<PERSON><PERSON><PERSON>", "Create gantt block": "Create gantt block", "Progress field": "Progress field", "Time scale": "Time scale", "Hour": "Hour", "Quarter of day": "Quarter of day", "Half of day": "Half of day", "Year": "Year", "QuarterYear": "QuarterYear", "Select grouping field": "Select grouping field", "Media": "Media", "Markdown": "<PERSON><PERSON>", "Wysiwyg": "Wysiwyg", "Chart blocks": "Chart blocks", "Column chart": "Column chart", "Bar chart": "Bar chart", "Line chart": "Line chart", "Pie chart": "Pie chart", "Area chart": "Area chart", "Other chart": "Other chart", "Other blocks": "Other blocks", "In configuration": "In configuration", "Chart title": "Chart title", "Chart type": "Chart type", "Chart config": "Chart config", "Templates": "Templates", "Template": "Template", "Select template": "Select template", "Action logs": "Action logs", "Create template": "Create template", "Edit markdown": "Edit markdown", "Add block": "Add block", "Add new": "Add new", "Add record": "Add record", "Add child": "Add child", "Collapse all": "Collapse all", "Expand all": "Expand all", "Expand/Collapse": "Expand/Collapse", "Default collapse": "Default collapse", "Tree table": "Tree table", "Custom field display name": "Custom field display name", "Display fields": "Display collection fields", "Edit record": "Edit record", "Delete menu item": "Delete menu item", "Add page": "Add page", "Add group": "Add group", "Add link": "Add link", "Insert above": "Insert above", "Insert below": "Insert below", "Save": "Save", "Delete block": "Delete block", "Are you sure you want to delete it?": "Are you sure you want to delete it?", "This is a demo text, **supports Markdown syntax**.": "This is a demo text, **supports Markdown syntax**.", "Filter": "Filter", "Connect data blocks": "Connect data blocks", "Action type": "Action type", "Actions": "Actions", "Insert": "Insert", "Insert if not exists": "Insert if not exists", "Insert if not exists, or update": "Insert if not exists, or update", "Determine whether a record exists by the following fields": "Determine whether a record exists by the following fields", "Update": "Update", "Update record": "Update record", "View": "View", "View record": "View record", "Refresh": "Refresh", "Data changes": "Data changes", "Field name": "Field name", "Before change": "Before change", "After change": "After change", "Delete record": "Delete record", "Delete collection": "Delete collection", "Create collection": "Create collection", "Collection display name": "Collection display name", "Collection name": "Collection name", "Inherits": "Inherits", "Primary key, unique identifier, self growth": "Primary key, unique identifier, self growth", "Store the creation user of each record": "Store the creation user of each record", "Store the last update user of each record": "Store the last update user of each record", "Store the creation time of each record": "Store the creation time of each record", "Store the last update time of each record": "Store the last update time of each record", "More options": "More options", "Records can be sorted": "Records can be sorted", "Calendar collection": "Calendar collection", "General collection": "General collection", "Connect to database view": "Connect to database view", "Sync from database": "Sync from database", "Source collections": "Source collections", "Field source": "Field source", "Preview": "Preview", "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.": "Randomly generated and can be modified. Support letters, numbers and underscores, must start with an letter.", "Edit": "Edit", "Edit collection": "Edit collection", "Configure fields": "Configure fields", "Configure columns": "Configure columns", "Edit field": "Edit field", "Override": "Override", "Override field": "Override field", "Configure fields of {{title}}": "Configure fields of {{title}}", "Association fields filter": "Association fields filter", "PK & FK fields": "PK & FK fields", "Association fields": "Association fields", "Choices fields": "Choices fields", "System fields": "System fields", "General fields": "General fields", "Inherited fields": "Inherited fields", "Parent collection fields": "Parent collection fields", "Basic": "Basic", "Single line text": "Single line text", "Long text": "Long text", "Phone": "Phone", "Email": "Email", "Number": "Number", "Integer": "Integer", "Percent": "Percent", "Password": "Password", "Advanced type": "Advanced", "Formula": "Formula", "Formula description": "Compute a value in each record based on other fields in the same record.", "Choices": "Choices", "Checkbox": "Checkbox", "Single select": "Single select", "Multiple select": "Multiple select", "Radio group": "Radio group", "Checkbox group": "Checkbox group", "China region": "China region", "Date & Time": "Date & Time", "Datetime": "Datetime", "Relation": "Relation", "Link to": "Link to", "Link to description": "Used to create collection relationships quickly and compatible with most common scenarios. Suitable for non-developer use. When present as a field, it is a drop-down selection used to select records from the target collection. Once created, it will simultaneously generate the associated fields of the current collection in the target collection.", "Sub-table": "Sub-table", "Sub-details": "Sub-details", "Sub-form(Popover)": "Sub-form(Popover)", "System info": "System info", "Created at": "Created at", "Last updated at": "Last updated at", "Created by": "Created by", "Last updated by": "Last updated by", "Add field": "Add field", "Field display name": "Field display name", "Field type": "Field type", "Field interface": "Field interface", "Date format": "Date format", "Year/Month/Day": "Year/Month/Day", "Year-Month-Day": "Year-Month-Day", "Day/Month/Year": "Day/Month/Year", "Show time": "Show time", "Time format": "Time format", "12 hour": "12 hour", "24 hour": "24 hour", "Relationship type": "Relationship type", "Inverse relationship type": "Inverse relationship type", "Source collection": "Source collection", "Source key": "Source key", "Target collection": "Target collection", "Through collection": "Through collection", "Target key": "Target key", "Foreign key": "Foreign key", "One to one": "One to one", "One to many": "One to many", "Many to one": "Many to one", "Many to many": "Many to many", "Foreign key 1": "Foreign key 1", "Foreign key 2": "Foreign key 2", "One to one description": "Used to create one-to-one relationships. For example, a user has a profile.", "One to many description": "Used to create a one-to-many relationship. For example, a country will have many cities and a city can only be in one country. When present as a field, it is a sub-table that displays the records of the associated collection. When created, a Many-to-one field is automatically generated in the associated collection.", "Many to one description": "Used to create many-to-one relationships. For example, a city can belong to only one country and a country can have many cities. When present as a field, it is a drop-down selection used to select record from the associated collection. Once created, a One-to-many field is automatically generated in the associated collection.", "Many to many description": "Used to create many-to-many relationships. For example, a student will have many teachers and a teacher will have many students. When present as a field, it is a drop-down selection used to select records from the associated collection.", "Generated automatically if left blank": "Generated automatically if left blank", "Display association fields": "Display association fields", "Display field title": "Display field title", "Field component": "Field component", "Allow multiple": "Allow multiple", "Quick upload": "Quick upload", "Select file": "Select file", "Subtable": "Sub-table", "Sub-form": "Sub-form", "Field mode": "Field mode", "Allow add new data": "Allow add new data", "Record picker": "Record picker", "Toggles the subfield mode": "Toggles the subfield mode", "Selector mode": "Selector mode", "Subtable mode": "Sub-table mode", "Subform mode": "Sub-form mode", "Edit block title": "Edit block title", "Block title": "Block title", "Pattern": "Pattern", "Operator": "Operator", "Editable": "Editable", "Readonly": "<PERSON><PERSON><PERSON>", "Easy-reading": "Easy-reading", "Add filter": "Add filter", "Add filter group": "Add filter group", "Comparision": "Comparision", "is": "is", "is not": "is not", "contains": "contains", "does not contain": "does not contain", "starts with": "starts with", "not starts with": "not starts with", "ends with": "ends with", "not ends with": "not ends with", "is empty": "is empty", "is not empty": "is not empty", "Edit chart": "Edit chart", "Add text": "Add text", "Filterable fields": "Filterable fields", "Edit button": "Edit button", "Hide": "<PERSON>de", "Enable actions": "Enable actions", "Import": "Import", "Export": "Export", "Customize": "Customize", "Custom": "Custom", "Function": "Function", "Popup form": "Popup form", "Flexible popup": "Flexible popup", "Configure actions": "Configure actions", "Display order number": "Display order number", "Enable drag and drop sorting": "Enable drag and drop sorting", "Triggered when the row is clicked": "Triggered when the row is clicked", "Add tab": "Add tab", "Disable tabs": "Disable tabs", "Details": "Details", "Edit form": "Edit form", "Create form": "Create form", "Form (Edit)": "Form (Edit)", "Form (Add new)": "Form (Add new)", "Edit tab": "Edit tab", "Relationship blocks": "Relationship blocks", "Select record": "Select record", "Display name": "Display name", "Select icon": "Select icon", "Custom column name": "Custom column name", "Edit description": "Edit description", "Required": "Required", "Unique": "Unique", "Primary": "Primary", "Auto increment": "Auto increment", "Label field": "Label field", "Default is the ID field": "Default is the ID field", "Set default sorting rules": "Set default sorting rules", "Set validation rules": "Set validation rules", "Max length": "Max length", "Min length": "Min length", "Maximum": "Maximum", "Minimum": "Minimum", "Max length must greater than min length": "Max length must greater than min length", "Min length must less than max length": "Min length must less than max length", "Maximum must greater than minimum": "Maximum must greater than minimum", "Minimum must less than maximum": "Minimum must less than maximum", "Validation rule": "Validation rule", "Add validation rule": "Add validation rule", "Format": "Format", "Regular expression": "Pattern", "Error message": "Error message", "Length": "Length", "The field value cannot be greater than ": "The field value cannot be greater than ", "The field value cannot be less than ": "The field value cannot be less than ", "The field value is not an integer number": "The field value is not an integer number", "Set default value": "Set default value", "Default value": "Default value", "is before": "is before", "is after": "is after", "is on or after": "is on or after", "is on or before": "is on or before", "is between": "is between", "Upload": "Upload", "Select level": "Select level", "Province": "Province", "City": "City", "Area": "Area", "Street": "Street", "Village": "Village", "Must select to the last level": "Must select to the last level", "Move {{title}} to": "Move {{title}} to", "Target position": "Target position", "After": "After", "Before": "Before", "Add {{type}} before \"{{title}}\"": "Add {{type}} before \"{{title}}\"", "Add {{type}} after \"{{title}}\"": "Add {{type}} after \"{{title}}\"", "Add {{type}} in \"{{title}}\"": "Add {{type}} in \"{{title}}\"", "Original name": "Original name", "Custom name": "Custom name", "Custom Title": "Custom Title", "Options": "Options", "Option value": "Option value", "Option label": "Option label", "Color": "Color", "Background Color": "Background Color", "Text Align": "Text Align", "Add option": "Add option", "Related collection": "Related collection", "Allow linking to multiple records": "Allow linking to multiple records", "Allow uploading multiple files": "Allow uploading multiple files", "Configure calendar": "Configure calendar", "Title field": "Title field", "Custom title": "Custom title", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Yearly": "Yearly", "Repeats": "Repeats", "Show lunar": "Show lunar", "Start date field": "Start date field", "End date field": "End date field", "Navigate": "Navigate", "Title": "Title", "Description": "Description", "Select view": "Select view", "Reset": "Reset", "Importable fields": "Importable fields", "Exportable fields": "Exportable fields", "Saved successfully": "Saved successfully", "Nickname": "Nickname", "Sign in": "Sign in", "Sign in via account": "Sign in via account", "Sign in via phone": "Sign in via phone", "Create an account": "Create an account", "Sign up": "Sign up", "Confirm password": "Confirm password", "Log in with an existing account": "Log in with an existing account", "Signed up successfully. It will jump to the login page.": "Signed up successfully. It will jump to the login page.", "Password mismatch": "Password mismatch", "Users": "Users", "Verification code": "Verification code", "Send code": "Send code", "Retry after {{count}} seconds": "Retry after {{count}} seconds", "Roles": "Roles", "Add role": "Add role", "Role name": "Role name", "Configure": "Configure", "Configure permissions": "Configure permissions", "Edit role": "Edit role", "Action permissions": "Action permissions", "Menu permissions": "Menu permissions", "Menu item name": "Menu item name", "Allow access": "Allow access", "Action name": "Action name", "Allow action": "Allow action", "Action scope": "Action scope", "Operate on new data": "Operate on new data", "Operate on existing data": "Operate on existing data", "Yes": "Yes", "No": "No", "Red": "Red", "Magenta": "Ma<PERSON><PERSON>", "Volcano": "Volcano", "Orange": "Orange", "Gold": "Gold", "Lime": "Lime", "Green": "Green", "Cyan": "<PERSON><PERSON>", "Blue": "Blue", "Geek blue": "Geek blue", "Purple": "Purple", "Default": "<PERSON><PERSON><PERSON>", "Add card": "Add card", "edit title": "edit title", "Turn pages": "Turn pages", "Others": "Others", "Other records": "Other records", "Save as template": "Save as template", "Save as block template": "Save as block template", "Block templates": "Block templates", "Block template": "Block template", "Convert template to duplicate": "Convert template to duplicate", "Template name": "Template name", "Block type": "Block type", "No blocks to connect": "No blocks to connect", "Action column": "Action column", "Records per page": "Records per page", "(Fields only)": "(Fields only)", "Button title": "Button title", "Button icon": "Button icon", "Submitted successfully": "Submitted successfully", "Operation succeeded": "Operation succeeded", "Operation failed": "Operation failed", "Open mode": "Open mode", "Popup size": "Popup size", "Small": "Small", "Middle": "Middle", "Large": "Large", "Size": "Size", "Oversized": "Oversized", "Auto": "Auto", "Object Fit": "Object Fit", "Cover": "Cover", "Fill": "Fill", "Contain": "Contain", "Scale Down": "Scale Down", "Menu item title": "Menu item title", "Menu item icon": "Menu item icon", "Target": "Target", "Position": "Position", "Insert before": "Insert before", "Insert after": "Insert after", "UI Editor": "UI Editor", "ASC": "ASC", "DESC": "DESC", "Add sort field": "Add sort field", "ID": "ID", "Identifier for program usage. Support letters, numbers and underscores, must start with an letter.": "Identifier for program usage. Support letters, numbers and underscores, must start with an letter.", "Drawer": "Drawer", "Dialog": "Dialog", "Delete action": "Delete action", "Custom column title": "Custom column title", "Column title": "column title", "Original title: ": "Original title: ", "Delete table column": "Delete table column", "Skip required validation": "<PERSON><PERSON> required validation", "Form values": "Form values", "Fields values": "Fields values", "The field has been deleted": "The field has been deleted", "When submitting the following fields, the saved values are": "When submitting the following fields, the saved values are", "After successful submission": "After successful submission", "Then": "Then", "Stay on current page": "Stay on current page", "Redirect to": "Redirect to", "Save action": "Save action", "Exists": "Exists", "Add condition": "Add condition", "Add condition group": "Add condition group", "exists": "exists", "not exists": "not exists", "Style": "Style", "=": "=", "≠": "≠", ">": ">", "≥": "≥", "<": "<", "≤": "≤", "Role UID": "Role UID", "Precision": "Precision", "Formula mode": "Formula mode", "Expression": "Expression", "Input +, -, *, /, ( ) to calculate, input @ to open field variables.": "Input +, -, *, /, ( ) to calculate, input @ to open field variables.", "Formula error.": "Formula error.", "Rich Text": "Rich Text", "Junction collection": "Junction collection", "Leave it blank, unless you need a custom intermediate table": "Leave it blank, unless you need a custom intermediate table", "Fields": "Fields", "Edit field title": "Edit field title", "Field title": "Field title", "Original field title: ": "Original field title: ", "Edit tooltip": "Edit tooltip", "Delete field": "Delete field", "Select collection": "Select collection", "Blank block": "Blank block", "Duplicate template": "Duplicate template", "Reference template": "Reference template", "Inherited template": "Inherited template", "Create calendar block": "Create calendar block", "Create kanban block": "Create kanban block", "Grouping field": "Grouping field", "Single select and radio fields can be used as the grouping field": "Single select and radio fields can be used as the grouping field", "Tab name": "Tab name", "Current record blocks": "Current record blocks", "Popup message": "Popup message", "Delete role": "Delete role", "Role display name": "Role display name", "Default role": "Default role", "All collections use general action permissions by default; permission configured individually will override the default one.": "All collections use general action permissions by default; permission configured individually will override the default one.", "Allows configuration of the whole system, including UI, collections, permissions, etc.": "Allows configuration of the whole system, including UI, collections, permissions, etc.", "New menu items are allowed to be accessed by default.": "New menu items are allowed to be accessed by default.", "Global permissions": "Global permissions", "General permissions": "General permissions", "Global action permissions": "Global action permissions", "General action permissions": "General action permissions", "Plugin settings permissions": "Plugin settings permissions", "Allow to desgin pages": "Allow to desgin pages", "Allow to manage plugins": "Allow to manage plugins", "Allow to configure plugins": "Allow to configure plugins", "Allows to configure interface": "Allows to configure interface", "Allows to install, activate, disable plugins": "Allows to install, activate, disable plugins", "Allows to configure plugins": "Allows to configure plugins", "Action display name": "Action display name", "Allow": "Allow", "Data scope": "Data scope", "Action on new records": "Action on new records", "Action on existing records": "Action on existing records", "All records": "All records", "Own records": "Own records", "Permission policy": "Permission policy", "Individual": "Individual", "General": "General", "Accessible": "Accessible", "Configure permission": "Configure permission", "Action permission": "Action permission", "Field permission": "Field permission", "Scope name": "Scope name", "Unsaved changes": "Unsaved changes", "Are you sure you don't want to save?": "Are you sure you don't want to save?", "Dragging": "Dragging", "Popup": "Popup", "Trigger workflow": "Trigger workflow", "Request API": "Request API", "Assign field values": "Assign field values", "Constant value": "Constant value", "Dynamic value": "Dynamic value", "Current user": "Current user", "Current role": "Current role", "Current record": "Current record", "Current collection": "Current collection", "Other collections": "Other collections", "Current popup record": "Current popup record", "Parent popup record": "Parent popup record", "Associated records": "Associated records", "Parent record": "Parent record", "Current time": "Current time", "System variables": "System variables", "Date variables": "Date variables", "Message popup close method": "Message popup close method", "Automatic close": "Automatic close", "Manually close": "Manually close", "After successful update": "After successful update", "Save record": "Save record", "Updated successfully": "Updated successfully", "After successful save": "After successful save", "After clicking the custom button, the following field values will be assigned according to the following form.": "After clicking the custom button, the following field values will be assigned according to the following form.", "After clicking the custom button, the following fields of the current record will be saved according to the following form.": "After clicking the custom button, the following fields of the current record will be saved according to the following form.", "Button background color": "Button background color", "Highlight": "Highlight", "Danger red": "Danger red", "Custom request": "Custom request", "Request settings": "Request settings", "Request URL": "Request URL", "Request method": "Request method", "Request query parameters": "Request query parameters", "Request headers": "Request headers", "Request body": "Request body", "Request success": "Request success", "Invalid JSON format": "Invalid JSON format", "After successful request": "After successful request", "Add exportable field": "Add exportable field", "Audit logs": "Audit logs", "Record ID": "Record ID", "User": "User", "Field": "Field", "Select": "Select", "Select field": "Select field", "Field value changes": "Field value changes", "One to one (has one)": "One to one (has one)", "One to one (belongs to)": "One to one (belongs to)", "Use the same time zone (GMT) for all users": "Use the same time zone (GMT) for all users", "Province/city/area name": "Province/city/area name", "Enabled languages": "Enabled languages", "View all plugins": "View all plugins", "Print": "Print", "Done": "Done", "Sign up successfully, and automatically jump to the sign in page": "Sign up successfully, and automatically jump to the sign in page", "File manager": "File manager", "ACL": "ACL", "Collection manager": "Collection manager", "Plugin manager": "Plugin manager", "Local": "Local", "Built-in": "Built-in", "Marketplace": "Marketplace", "Add plugin": "Add plugin", "Plugin source": "Plugin source", "Upgrade": "Upgrade", "Plugin dependencies check failed": "Plugin dependencies check failed", "More details": "More details", "Upload new version": "Upload new version", "Version": "Version", "Npm package": "Npm package", "Npm package name": "Npm package name", "Upload plugin": "Upload plugin", "Official plugin": "Official plugin", "Add type": "Add type", "Changelog": "Changelog", "Dependencies check": "Dependencies check", "Update plugin": "Update plugin", "Installing": "Installing", "The deletion was successful.": "The deletion was successful.", "Plugin Zip File": "Plugin Zip File", "Compressed file url": "Compressed file url", "Last updated": "Last updated", "PackageName": "PackageName", "DisplayName": "DisplayName", "Readme": "<PERSON><PERSON>", "Dependencies compatibility check": "Dependencies compatibility check", "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.": "Plugin dependencies check failed, you should change the dependent version to meet the version requirements.", "Version range": "Version range", "Plugin's version": "Plugin's version", "Result": "Result", "No CHANGELOG.md file": "No CHANGELOG.md file", "No README.md file": "No README.md file", "Homepage": "Homepage", "Drag and drop the file here or click to upload, file size should not exceed 30M": "Drag and drop the file here or click to upload, file size should not exceed 30M", "Dependencies check failed, can't enable.": "Dependencies check failed, can't enable.", "Plugin starting...": "Plugin starting...", "Plugin stopping...": "Plugin stopping...", "Are you sure to delete this plugin?": "Are you sure to delete this plugin?", "Are you sure to disable this plugin?": "Are you sure to disable this plugin?", "re-download file": "re-download file", "Not enabled": "Not enabled", "Search plugin": "Search plugin", "Author": "Author", "Plugin loading failed. Please check the server logs.": "Plugin loading failed. Please check the server logs.", "Coming soon...": "Coming soon...", "All plugin settings": "All plugin settings", "Bookmark": "Bookmark", "Manage all settings": "Manage all settings", "Create inverse field in the target collection": "Create inverse field in the target collection", "Inverse field name": "Inverse field name", "Inverse field display name": "Inverse field display name", "Bulk update": "Bulk update", "After successful bulk update": "After successful bulk update", "Bulk edit": "Bulk edit", "Data will be updated": "Data will be updated", "Selected": "Selected", "All": "All", "Update selected data?": "Update selected data?", "Update all data?": "Update all data?", "Remains the same": "<PERSON><PERSON><PERSON> the same", "Changed to": "Changed to", "Clear": "Clear", "Add attach": "Add attach", "Please select the records to be updated": "Please select the records to be updated", "Selector": "Selector", "Inner": "Inner", "Search and select collection": "Search and select collection", "Please fill in the iframe URL": "Please fill in the iframe URL", "Fix block": "Fix block", "Plugin name": "Plugin name", "Plugin tab name": "Plugin tab name", "AutoGenId": "Auto-generated ID field", "CreatedBy": "CreatedBy", "UpdatedBy": "UpdatedBy", "CreatedAt": "CreatedAt", "UpdatedAt": "UpdatedAt", "Column width": "Column width", "Sortable": "Sortable", "Enable link": "Enable link", "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>": "This is likely a NocoBase internals bug. Please open an issue at <1>here</1>", "Render Failed": "Render Failed", "App error": "App error", "Feedback": "<PERSON><PERSON><PERSON>", "Try again": "Try again", "Download logs": "Download logs", "Data template": "Data template", "Duplicate": "Duplicate", "Duplicating": "Duplicating", "Duplicate mode": "Duplicate mode", "Quick duplicate": "Quick duplicate", "Duplicate and continue": "Duplicate and continue", "Please configure the duplicate fields": "Please configure the duplicate fields", "Add": "Add", "Add new mode": "Add new mode", "Quick add": "Quick add", "Modal add": "Modal add", "Save mode": "Save mode", "First or create": "First or create", "Update or create": "Update or create", "Find by the following fields": "Find by the following fields", "Create": "Create", "Current form": "Current form", "Current object": "Current object", "Linkage with form fields": "Linkage with form fields", "Allow add new, update and delete actions": "Allow add new, update and delete actions", "Date display format": "Date display format", "Assign  data scope for the template": "Assign  data scope for the template", "Table selected records": "Table selected records", "Tag": "Tag", "Tag color field": "Tag color field", "Sync successfully": "Sync successfully", "Sync from form fields": "Sync from form fields", "Select all": "Select all", "Restart": "<PERSON><PERSON>", "Restart application": "Restart application", "Cascade Select": "Cascade Select", "Execute": "Execute", "Please use a valid SELECT or WITH AS statement": "Please use a valid SELECT or WITH AS statement", "Please confirm the SQL statement first": "Please confirm the SQL statement first", "Automatically drop objects that depend on the collection (such as views), and in turn all objects that depend on those objects": "Automatically drop objects that depend on the collection (such as views), and in turn all objects that depend on those objects", "Sign in with another account": "Sign in with another account", "Return to the main application": "Return to the main application", "Permission deined": "Permission denied", "loading": "loading", "name is required": "name is required", "data source": "data source", "Data source": "Data source", "DataSource": "DataSource", "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.": "The {{type}} \"{{name}}\" may have been deleted. Please remove this {{blockType}}.", "Preset fields": "Preset fields", "Home page": "Home page", "Handbook": "Handbook", "License": "License", "Generic properties": "Generic properties", "Specific properties": "Specific properties", "Used for drag and drop sorting scenarios, supporting grouping sorting": "Used for drag and drop sorting scenarios, supporting grouping sorting", "Grouped sorting": "Grouped sorting", "When a field is selected for grouping, it will be grouped first before sorting.": "When a field is selected for grouping, it will be grouped first before sorting.", "Departments": "Departments", "Main department": "Main department", "Department name": "Department name", "Superior department": "Superior department", "Owners": "Owners", "Plugin settings": "Plugin settings", "Menu": "<PERSON><PERSON>", "Drag and drop sorting field": "Drag and drop sorting field", "This variable has been deprecated and can be replaced with \"Current form\"": "This variable has been deprecated and can be replaced with \"Current form\"", "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.": "The value of this variable is derived from the query string of the page URL. This variable can only be used normally when the page has a query string.", "URL search params": "URL search params", "Expand All": "Expand All", "Search": "Search", "Clear default value": "Clear default value", "Open in new window": "Open in new window", "Sorry, the page you visited does not exist.": "Sorry, the page you visited does not exist.", "is none of": "is none of", "is any of": "is any of", "Plugin dependency version mismatch": "Plugin dependency version mismatch", "The current dependency version of the plugin does not match the version of the application and may not work properly. Are you sure you want to continue enabling the plugin?": "The current dependency version of the plugin does not match the version of the application and may not work properly. Are you sure you want to continue enabling the plugin?", "Allow multiple selection": "Allow multiple selection", "Parent object": "Parent object", "Skip getting the total number of table records during paging to speed up loading. It is recommended to enable this option for data tables with a large amount of data": "Skip getting the total number of table records during paging to speed up loading. It is recommended to enable this option for data tables with a large amount of data", "Enable secondary confirmation": "Enable secondary confirmation", "Notification": "Notification", "Ellipsis overflow content": "Ellipsis overflow content", "Hide column": "Hide column", "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.": "In configuration mode, the entire column becomes transparent. In non-configuration mode, the entire column will be hidden. Even if the entire column is hidden, its configured default values and other settings will still take effect.", "Unauthenticated. Please sign in to continue.": "Unauthenticated. Please sign in to continue.", "User not found. Please sign in again to continue.": "User not found. Please sign in again to continue.", "Your session has expired. Please sign in again.": "Your session has expired. Please sign in again.", "User password changed, please signin again.": "User password changed, please signin again.", "Desktop routes": "Desktop routes", "Route permissions": "Route permissions", "New routes are allowed to be accessed by default": "New routes are allowed to be accessed by default", "Route name": "Route name", "Mobile routes": "Mobile routes", "Show in menu": "Show in menu", "Hide in menu": "Hide in menu", "Path": "Path", "Type": "Type", "Access": "Access", "Routes": "Routes", "Add child route": "Add child", "Delete routes": "Delete routes", "Delete route": "Delete route", "Are you sure you want to hide these routes in menu?": "Are you sure you want to hide these routes in menu?", "Are you sure you want to show these routes in menu?": "Are you sure you want to show these routes in menu?", "Are you sure you want to hide this menu?": "Are you sure you want to hide this menu?", "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.": "After hiding, this menu will no longer appear in the menu bar. To show it again, you need to go to the route management page to configure it.", "If selected, the page will display Tab pages.": "If selected, the page will display Tab pages.", "If selected, the route will be displayed in the menu.": "If selected, the route will be displayed in the menu.", "Are you sure you want to hide this tab?": "Are you sure you want to hide this tab?", "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.": "After hiding, this tab will no longer appear in the tab bar. To show it again, you need to go to the route management page to set it.", "No pages yet, please configure first": "No pages yet, please configure first", "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode": "Click the \"UI Editor\" icon in the upper right corner to enter the UI Editor mode", "Specifies a Permissions Policy for the <iframe>. The policy defines what features are available to the <iframe> (for example, access to the microphone, camera, battery, web-share, etc.) based on the origin of the request.": "Specifies a Permissions Policy for the <iframe>. The policy defines what features are available to the <iframe> (for example, access to the microphone, camera, battery, web-share, etc.) based on the origin of the request.", "Deprecated": "Deprecated", "Full permissions": "Full permissions", "Refresh data blocks": "Refresh data blocks", "Select data blocks to refresh": "Select data blocks to refresh", "After successful submission, the selected data blocks will be automatically refreshed.": "After successful submission, the selected data blocks will be automatically refreshed.", "Reset link expiration": "Reset link expiration"}