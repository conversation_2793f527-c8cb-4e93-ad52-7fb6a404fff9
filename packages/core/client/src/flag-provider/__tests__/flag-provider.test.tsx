/**
 * This file is part of the NocoBase (R) project.
 * Copyright (c) 2020-2024 NocoBase Co., Ltd.
 * Authors: <AUTHORS>
 *
 * This project is dual-licensed under AGPL-3.0 and NocoBase Commercial License.
 * For more information, please refer to: https://www.nocobase.com/agreement.
 */

import { renderHook } from '@nocobase/test/client';
import React from 'react';
import { FlagProvider } from '../FlagProvider';
import { useFlag } from '../hooks/useFlag';

describe('FlagProvider', () => {
  it('should render', () => {
    const { result } = renderHook(() => useFlag(), {
      wrapper: ({ children }) => {
        return <FlagProvider isInAssignFieldValues={true}>{children}</FlagProvider>;
      },
    });

    expect(result.current.isInAssignFieldValues).toBe(true);
  });
});
