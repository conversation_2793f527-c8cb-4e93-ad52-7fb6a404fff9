version: "3"
networks:
  nocobase:
    driver: bridge
services:
  app:
    image: nocobase/nocobase:latest
    networks:
      - nocobase
    environment:
      - APP_KEY=your-secret-key # Replace it with your own app key
      - ENCRYPTION_FIELD_KEY=your-secret-key # Replace it with your own app key
      - DB_DIALECT=postgres
      - DB_HOST=postgres
      - DB_DATABASE=nocobase
      - DB_USER=nocobase
      - DB_PASSWORD=nocobase
    volumes:
      - ./storage:/app/nocobase/storage
    ports:
      - "13000:80"
    depends_on:
      - postgres
    init: true
  postgres:
    image: postgres:10
    restart: always
    command: postgres -c wal_level=logical
    environment:
      POSTGRES_USER: nocobase
      POSTGRES_DB: nocobase
      POSTGRES_PASSWORD: nocobase
    volumes:
      - ./storage/db/postgres:/var/lib/postgresql/data
    networks:
      - nocobase
