name: Manual build pro image

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ inputs.pr_number }}-${{ inputs.nocobase_pr_number }}
  cancel-in-progress: true

run-name: Build pro image ${{ github.ref }}-${{ inputs.pr_number }}-${{ inputs.nocobase_pr_number }}

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'Please enter the pr number of pro-plugins'
      nocobase_pr_number:
        description: 'Please enter the pr number of nocobase/nocobase repository'

jobs:
  get-plugins:
    uses: nocobase/nocobase/.github/workflows/get-plugins.yml@main
    secrets: inherit
  build-and-push:
    if: github.event.pull_request.head.repo.fork != true
    runs-on: ubuntu-latest
    needs: get-plugins
    services:
      verdaccio:
        image: verdaccio/verdaccio:latest
        ports:
          - 4873:4873
    steps:
      - name: Get pro plugins
        id: get-info
        run: |
          if [[ "${{ github.head_ref || github.ref_name }}" == "next" ]]; then
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.beta-plugins }}')" >> $GITHUB_OUTPUT
            echo "branch=$(echo 'next')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.head_ref || github.ref_name }}" == "develop" ]]; then
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.alpha-plugins }}')" >> $GITHUB_OUTPUT
            echo "branch=$(echo 'develop')" >> $GITHUB_OUTPUT
          else
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.rc-plugins }}')" >> $GITHUB_OUTPUT
            echo "branch=$(echo 'main')" >> $GITHUB_OUTPUT
          fi
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: nocobase,pro-plugins,${{ join(fromJSON(steps.get-info.outputs.proRepos), ',') }}
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref || github.ref_name }}
          token: ${{ steps.app-token.outputs.token }}
          submodules: true
      - name: Checkout nocobase/nocobase pr
        if: ${{ inputs.nocobase_pr_number != '' }}
        shell: bash
        run: |
          gh pr checkout ${{ inputs.nocobase_pr_number }}
        env:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Checkout pro-plugins
        uses: actions/checkout@v4
        with:
          repository: nocobase/pro-plugins
          path: packages/pro-plugins
          ref: ${{ github.head_ref || github.ref_name }}
          token: ${{ steps.app-token.outputs.token }}
      - name: Checkout pr
        if: ${{ inputs.pr_number != '' }}
        shell: bash
        run: |
          cd ./packages/pro-plugins/
          gh pr checkout ${{ inputs.pr_number }}
          cd ../../
        env:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Clone pro repos
        shell: bash
        run: |
          for repo in ${{ join(fromJSON(steps.get-info.outputs.proRepos), ' ') }}
          do
          git clone -b ${{ steps.get-info.outputs.branch }} https://x-access-token:${{ steps.app-token.outputs.token }}@github.com/nocobase/$repo.git packages/pro-plugins/@nocobase/$repo
          done
      - name: rm .git
        run: |
          rm -rf packages/pro-plugins/.git
          for repo in ${{ join(fromJSON(steps.get-info.outputs.proRepos), ' ') }}
          do
            rm -rf packages/pro-plugins/@nocobase/$repo/.git
          done
          git config --global user.email "<EMAIL>"
          git config --global user.name "Your Name" && git add -A && git commit -m "tmp commit"
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: network=host
      - name: Login to Aliyun Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALI_DOCKER_REGISTRY }}
          username: ${{ secrets.ALI_DOCKER_USERNAME }}
          password: ${{ secrets.ALI_DOCKER_PASSWORD }}
      - name: Get tag
        id: get-tag
        run: |
          if [ "${{ inputs.pr_number }}" != "" ]; then
            echo "tag=pr-${{ inputs.pr_number }}-pro" >> "$GITHUB_OUTPUT"
          elif [ "${{ inputs.nocobase_pr_number }}" != "" ]; then
            echo "tag=pr-${{ inputs.nocobase_pr_number }}" >> "$GITHUB_OUTPUT"
          else
            echo "tag=${{ github.head_ref || github.ref_name }}" >> "$GITHUB_OUTPUT"
          fi
      - name: Set tags
        id: set-tags
        run: |
          echo "::set-output name=tags::${{ secrets.ALI_DOCKER_REGISTRY }}/nocobase/nocobase:${{ steps.get-tag.outputs.tag }}"
      - name: Set variables
        run: |
          target_directory="./packages/pro-plugins/@nocobase"
          subdirectories=$(find "$target_directory" -mindepth 1 -maxdepth 1 -type d -exec basename {} \; | tr '\n' ' ')
          trimmed_variable=$(echo "$subdirectories" | xargs)
          packageNames="@nocobase/${trimmed_variable// / @nocobase/}"
          pluginNames="${trimmed_variable//plugin-/}"
          BEFORE_PACK_NOCOBASE="yarn add @nocobase/plugin-notifications @nocobase/plugin-disable-pm-add $packageNames -W --production"
          APPEND_PRESET_LOCAL_PLUGINS="notifications,disable-pm-add,${pluginNames// /,}"
          echo "var1=$BEFORE_PACK_NOCOBASE" >> $GITHUB_OUTPUT
          echo "var2=$APPEND_PRESET_LOCAL_PLUGINS" >> $GITHUB_OUTPUT
        id: vars
      - name: Build and push - ${{ steps.get-tag.outputs.tag }}
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile.pro
          build-args: |
            VERDACCIO_URL=http://localhost:4873/
            COMMIT_HASH=${GITHUB_SHA}
            PLUGINS_DIRS=pro-plugins
            BEFORE_PACK_NOCOBASE=${{ steps.vars.outputs.var1 }}
            APPEND_PRESET_LOCAL_PLUGINS=${{ steps.vars.outputs.var2 }}
          push: true
          tags: ${{ steps.set-tags.outputs.tags }}
      - name: Deploy NocoBase
        run: |
          curl --retry 2 --location --request POST "${{secrets.NOCOBASE_DEPLOY_HOST}}${{ steps.get-tag.outputs.tag }}" \
          --header 'Content-Type: application/json' \
          -d "{
              \"tag\": \"${{ steps.get-tag.outputs.tag }}\",
              \"dialect\": \"postgres\"
          }"
      - name: Deploy NocoBase V2
        run: |
          curl --retry 2 --location --request POST "${{secrets.NOCOBASE_DEPLOY_HOST_V2}}${{ steps.get-tag.outputs.tag }}" \
          --header 'Content-Type: application/json' \
          -d "{
              \"tag\": \"${{ steps.get-tag.outputs.tag }}\",
              \"dialect\": \"postgres\"
          }"
