name: NocoBase backend test

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:

  # push:
  #   branches:
  #     - main
  #     - next
  #     - develop
  #   paths:
  #     - 'package.json'
  #     - '**/yarn.lock'
  #     - 'packages/core/acl/**'
  #     - 'packages/core/auth/**'
  #     - 'packages/core/actions/**'
  #     - 'packages/core/database/**'
  #     - 'packages/core/resourcer/**'
  #     - 'packages/core/data-source-manager/**'
  #     - 'packages/core/server/**'
  #     - 'packages/core/utils/**'
  #     - 'packages/plugins/**/src/server/**'
  #     - '.github/workflows/nocobase-test-backend.yml'
  # pull_request:
  #   paths:
  #     - 'package.json'
  #     - '**/yarn.lock'
  #     - 'packages/core/acl/**'
  #     - 'packages/core/auth/**'
  #     - 'packages/core/actions/**'
  #     - 'packages/core/database/**'
  #     - 'packages/core/resourcer/**'
  #     - 'packages/core/data-source-manager/**'
  #     - 'packages/core/server/**'
  #     - 'packages/core/utils/**'
  #     - 'packages/plugins/**/src/server/**'
  #     - '.github/workflows/nocobase-test-backend.yml'

jobs:
  sqlite-test:
    strategy:
      matrix:
        node_version: ['20']
        underscored: [true, false]
    runs-on: ubuntu-latest
    container: node:${{ matrix.node_version }}
    services:
      redis:
        image: redis/redis-stack-server:latest
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: 'yarn'
      - name: Install project dependencies
        run: |
          yarn install
          yarn add sqlite3 --no-save -W
      - name: Test with Sqlite
        run: yarn test --server --single-thread=false
        env:
          LOGGER_LEVEL: error
          DB_DIALECT: sqlite
          DB_STORAGE: /tmp/db.sqlite
          DB_TEST_PREFIX: test_
          DB_UNDERSCORED: ${{ matrix.underscored }}
          ENCRYPTION_FIELD_KEY: 1%&glK;<UA}aIxJVc53-4G(rTi0vg@J]

  postgres-test:
    strategy:
      matrix:
        node_version: ['20']
        underscored: [true, false]
        schema: [public, nocobase]
        collection_schema: [public, user_schema]
        cache_redis: ['']
        include:
          - node_version: 20
            underscored: true
            schema: public
            collection_schema: public
            cache_redis: redis://redis:6379/0
    runs-on: ubuntu-latest
    container: node:${{ matrix.node_version }}
    services:
      # Label used to access the service container
      postgres:
        # Docker Hub image
        image: postgres:11
        # Provide the password for postgres
        env:
          POSTGRES_USER: nocobase
          POSTGRES_PASSWORD: password
        # Set health checks to wait until postgres has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis/redis-stack-server:latest
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: 'yarn'

      - name: Install project dependencies
        run: yarn install
      - name: Test with postgres
        run: |
          ./node_modules/.bin/tsx packages/core/test/src/scripts/test-db-creator.ts &
          sleep 1
          yarn test --server --single-thread=false
        env:
          LOGGER_LEVEL: error
          DB_DIALECT: postgres
          DB_HOST: postgres
          DB_PORT: 5432
          DB_USER: nocobase
          DB_PASSWORD: password
          DB_DATABASE: nocobase
          DB_UNDERSCORED: ${{ matrix.underscored }}
          DB_SCHEMA: ${{ matrix.schema }}
          COLLECTION_MANAGER_SCHEMA: ${{ matrix.collection_schema }}
          DB_TEST_DISTRIBUTOR_PORT: 23450
          DB_TEST_PREFIX: test
          ENCRYPTION_FIELD_KEY: 1%&glK;<UA}aIxJVc53-4G(rTi0vg@J]
          CACHE_REDIS_URL: ${{ matrix.cache_redis }}
    timeout-minutes: 60

  mysql-test:
    strategy:
      matrix:
        node_version: ['20']
        underscored: [true, false]
    runs-on: ubuntu-latest
    container: node:${{ matrix.node_version }}
    services:
      mysql:
        image: mysql:8.2 # >= 8.3 will meet unsolved error: https://github.com/nocobase/nocobase/actions/runs/**********/job/20653828990?pr=3383
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: nocobase
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      redis:
        image: redis/redis-stack-server:latest
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: 'yarn'

      - name: Install project dependencies
        run: yarn install
      - name: Test with MySQL
        run: |
          ./node_modules/.bin/tsx packages/core/test/src/scripts/test-db-creator.ts &
          sleep 1
          yarn test --server --single-thread=false
        env:
          LOGGER_LEVEL: error
          DB_DIALECT: mysql
          DB_HOST: mysql
          DB_PORT: 3306
          DB_USER: root
          DB_PASSWORD: password
          DB_DATABASE: nocobase
          DB_UNDERSCORED: ${{ matrix.underscored }}
          DB_TEST_DISTRIBUTOR_PORT: 23450
          DB_TEST_PREFIX: test_
          ENCRYPTION_FIELD_KEY: 1%&glK;<UA}aIxJVc53-4G(rTi0vg@J]
    timeout-minutes: 60
  mariadb-test:
    strategy:
      matrix:
        node_version: ['20']
        underscored: [true, false]
    runs-on: ubuntu-latest
    container: node:${{ matrix.node_version }}
    services:
      mariadb:
        image: mariadb:10.9
        env:
          MARIADB_ROOT_PASSWORD: password
          MARIADB_DATABASE: nocobase
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      redis:
        image: redis/redis-stack-server:latest
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: 'yarn'

      - name: Install project dependencies
        run: yarn install
      - name: Install project dependencies
        run: yarn --prefer-offline
      - name: Test with MariaDB
        run: |
          ./node_modules/.bin/tsx packages/core/test/src/scripts/test-db-creator.ts &
          sleep 1
          yarn test --server --single-thread=false
        env:
          LOGGER_LEVEL: error
          DB_DIALECT: mariadb
          DB_HOST: mariadb
          DB_PORT: 3306
          DB_USER: root
          DB_PASSWORD: password
          DB_DATABASE: nocobase
          DB_UNDERSCORED: ${{ matrix.underscored }}
          DB_TEST_DISTRIBUTOR_PORT: 23450
          DB_TEST_PREFIX: test_
          ENCRYPTION_FIELD_KEY: 1%&glK;<UA}aIxJVc53-4G(rTi0vg@J]
    timeout-minutes: 60
