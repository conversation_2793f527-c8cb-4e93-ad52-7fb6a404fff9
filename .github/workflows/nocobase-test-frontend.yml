name: NocoBase frontEnd test

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - main
      - next
      - develop
    paths:
      - 'packages/**'
      - '**/yarn.lock'
      - '.github/workflows/nocobase-test-frontend.yml'
      - '!packages/core/acl/**'
      - '!packages/core/actions/**'
      - '!packages/core/database/**'
      - '!packages/core/server/**'
      - '!packages/plugins/**/src/server/**'
  pull_request:
    paths:
      - '.github/workflows/nocobase-test-frontend.yml'
      - 'packages/**'
      - '**/yarn.lock'
      - '!packages/core/acl/**'
      - '!packages/core/actions/**'
      - '!packages/core/database/**'
      - '!packages/core/server/**'
      - '!packages/plugins/**/src/server/**'

jobs:
  frontend-test:
    strategy:
      matrix:
        node_version: ['20']
    runs-on: ubuntu-latest
    container: node:${{ matrix.node_version }}
    steps:
      - uses: actions/checkout@v2

      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: 'yarn'

      - run: yarn install
      - run: yarn test:client
    timeout-minutes: 30
