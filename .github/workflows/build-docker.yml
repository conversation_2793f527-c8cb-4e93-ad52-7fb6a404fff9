name: Build full docker image

on:
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name'
        required: true

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ inputs.tag_name }}
  cancel-in-progress: true

jobs:
  push-docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Login to Aliyun Container Registry (Public)
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALI_DOCKER_PUBLIC_REGISTRY }}
          username: ${{ secrets.ALI_DOCKER_USERNAME }}
          password: ${{ secrets.ALI_DOCKER_PASSWORD }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: ./docker/nocobase
          file: ./docker/nocobase/Dockerfile-full
          build-args: |
            CNA_VERSION=${{ inputs.tag_name }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: nocobase/nocobase:${{ inputs.tag_name }}-full,${{ secrets.ALI_DOCKER_PUBLIC_REGISTRY }}/nocobase/nocobase:${{ inputs.tag_name }}-full
