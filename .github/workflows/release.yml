name: Release

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'

jobs:
  get-plugins:
    uses: nocobase/nocobase/.github/workflows/get-plugins.yml@main
    secrets: inherit
  publish-npm:
    needs: get-plugins
    runs-on: ubuntu-latest
    steps:
      - name: Set Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Get info
        id: get-info
        shell: bash
        run: |
          if [[ "${{ github.ref_name }}" =~ "beta" ]]; then
            echo "defaultTag=$(echo 'beta')" >> $GITHUB_OUTPUT
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.beta-plugins }}')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref_name }}" =~ "alpha" ]]; then
            echo "defaultTag=$(echo 'alpha')" >> $GITHUB_OUTPUT
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.alpha-plugins }}')" >> $GITHUB_OUTPUT
          else
            # rc
            echo "defaultTag=$(echo 'latest')" >> $GITHUB_OUTPUT
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.rc-plugins }}')" >> $GITHUB_OUTPUT
          fi
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: nocobase,pro-plugins,${{ join(fromJSON(steps.get-info.outputs.proRepos), ',') }},${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ',') }}
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref_name }}
      - name: Send curl request and parse response
        env:
          PKG_USERNAME: ${{ secrets.PKG_USERNAME }}
          PKG_PASSWORD: ${{ secrets.PKG_PASSWORD }}
        run: |
          mkdir git-ci-cache
          apt-get update && apt-get install -y jq
          response1=$(curl -s 'https://pkg.nocobase.com/-/verdaccio/sec/login' \
            -H 'content-type: application/json' \
            --data-raw '{"username":"'$PKG_USERNAME'","password":"'$PKG_PASSWORD'"}')
          token1=$(echo $response1 | jq -r '.token')
          response2=$(curl -s 'https://pkg-src.nocobase.com/-/verdaccio/sec/login' \
            -H 'content-type: application/json' \
            --data-raw '{"username":"'$PKG_USERNAME'","password":"'$PKG_PASSWORD'"}')
          token2=$(echo $response2 | jq -r '.token')
          echo "PKG_NOCOBASE_TOKEN=$token1" >> $GITHUB_ENV
          echo "PKG_SRC_NOCOBASE_TOKEN=$token2" >> $GITHUB_ENV
      - name: yarn install and build
        run: |
          yarn config set registry https://registry.npmjs.org/
          yarn install
          yarn build
      - name: publish npmjs.org
        continue-on-error: true
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "test"
          git config --global --add safe.directory /__w/nocobase/nocobase
          echo "# test" >> Release.md
          git add .
          git commit -m "chore(versions): test publish packages xxx"
          npm config set access public
          npm config set registry https://registry.npmjs.org/
          npm config set //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}
          yarn config set access public
          yarn config set registry https://registry.npmjs.org/
          yarn config set //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}
          npm whoami
          yarn release:force --no-verify-access --no-git-reset --registry https://registry.npmjs.org/ --dist-tag=${{ steps.get-info.outputs.defaultTag }}
      - name: Checkout pro-plugins
        uses: actions/checkout@v3
        with:
          repository: nocobase/pro-plugins
          path: packages/pro-plugins
          ref: ${{ github.ref_name }}
          token: ${{ steps.app-token.outputs.token }}
      - name: Clone pro repos
        shell: bash
        run: |
          for repo in ${{ join(fromJSON(steps.get-info.outputs.proRepos), ' ') }} ${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ' ') }}
          do
          git clone -b ${{ github.ref_name }} https://x-access-token:${{ steps.app-token.outputs.token }}@github.com/nocobase/$repo.git packages/pro-plugins/@nocobase/$repo
          done
      - name: Build Pro plugins
        run: |
          yarn config set registry https://registry.npmjs.org/
          yarn install
          yarn build packages/pro-plugins
      - name: publish pkg.nocobase.com
        run: |
          git reset --hard
          npm config set //pkg.nocobase.com/:_authToken=${{ env.PKG_NOCOBASE_TOKEN }}
          yarn release:force --no-verify-access --no-git-reset --registry https://pkg.nocobase.com --dist-tag=${{ steps.get-info.outputs.defaultTag }}
      - name: publish pkg-src.nocobase.com
        run: |
          git reset --hard
          bash generate-npmignore.sh ignore-src
          npm config set //pkg-src.nocobase.com/:_authToken=${{ env.PKG_SRC_NOCOBASE_TOKEN }}
          yarn release:force --no-verify-access --no-git-reset --registry https://pkg-src.nocobase.com --dist-tag=${{ steps.get-info.outputs.defaultTag }}
  push-docker:
    runs-on: ubuntu-latest
    needs:
      - get-plugins
      - publish-npm
    steps:
      - name: Get info
        id: get-info
        shell: bash
        run: |
          echo "cnaVersion=${GITHUB_REF_NAME#v}" >> "$GITHUB_OUTPUT"
          if [[ "${{ github.ref_name }}" =~ "beta" ]]; then
            echo "defaultTag=$(echo 'beta')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref_name }}" =~ "alpha" ]]; then
            echo "defaultTag=$(echo 'alpha')" >> $GITHUB_OUTPUT
          else
            # rc
            echo "defaultTag=$(echo 'latest')" >> $GITHUB_OUTPUT
          fi
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref_name }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            nocobase/nocobase
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
      - name: Prepare all tags
        id: prepare-tags
        run: |
          DEFAULT_TAG=${{ steps.get-info.outputs.defaultTag }}
          META_TAGS="${{ steps.meta.outputs.tags }}"
          ALI_REG="${{ secrets.ALI_DOCKER_PUBLIC_REGISTRY }}"

          TAGS=()

          TAGS+=("nocobase/nocobase:$DEFAULT_TAG")
          TAGS+=("$ALI_REG/nocobase/nocobase:$DEFAULT_TAG")

          IFS=',' read -ra TAG_ARRAY <<< "$META_TAGS"
          for TAG in "${TAG_ARRAY[@]}"; do
            TAGS+=("$TAG")
            TAGS+=("$ALI_REG/$TAG")
          done

          ALL_TAGS=$(IFS=, ; echo "${TAGS[*]}")
          echo "ALL_TAGS=$ALL_TAGS" >> $GITHUB_ENV
      - name: Prepare all full tags
        id: prepare-full-tags
        run: |
          DEFAULT_TAG=${{ steps.get-info.outputs.defaultTag }}
          META_TAGS="${{ steps.meta.outputs.tags }}"
          ALI_REG="${{ secrets.ALI_DOCKER_PUBLIC_REGISTRY }}"

          TAGS=()

          TAGS+=("nocobase/nocobase:$DEFAULT_TAG-full")
          TAGS+=("$ALI_REG/nocobase/nocobase:$DEFAULT_TAG-full")

          IFS=',' read -ra TAG_ARRAY <<< "$META_TAGS"
          for TAG in "${TAG_ARRAY[@]}"; do
            TAGS+=("$TAG-full")
            TAGS+=("$ALI_REG/$TAG-full")
          done

          ALL_TAGS=$(IFS=, ; echo "${TAGS[*]}")
          echo "ALL_FULL_TAGS=$ALL_TAGS" >> $GITHUB_ENV
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Login to Aliyun Container Registry (Public)
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALI_DOCKER_PUBLIC_REGISTRY }}
          username: ${{ secrets.ALI_DOCKER_USERNAME }}
          password: ${{ secrets.ALI_DOCKER_PASSWORD }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: ./docker/nocobase
          build-args: |
            CNA_VERSION=${{ steps.get-info.outputs.cnaVersion }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.ALL_TAGS }}
      - name: Build and push (Full)
        uses: docker/build-push-action@v3
        with:
          context: ./docker/nocobase
          file: ./docker/nocobase/Dockerfile-full
          build-args: |
            CNA_VERSION=${{ steps.get-info.outputs.cnaVersion }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.ALL_FULL_TAGS }}
