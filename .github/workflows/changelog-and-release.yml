name: Write changelog and create release

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      version:
        type: choice
        description: Please choose a version
        options:
          - rc
          - beta
          - alpha
        default: rc
  push:
    tags:
      - 'v*'

jobs:
  get-plugins:
    uses: nocobase/nocobase/.github/workflows/get-plugins.yml@main
    secrets: inherit
  write-changelog-and-release:
    needs: get-plugins
    runs-on: ubuntu-latest
    steps:
      - name: Get info
        id: get-info
        shell: bash
        run: |
          if [[ "${{ inputs.version }}" == "beta" || ${{ github.ref_name }} =~ "beta" ]]; then
            echo "branch=$(echo 'next')" >> $GITHUB_OUTPUT
            echo "version=$(echo 'beta')" >> $GITHUB_OUTPUT
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.beta-plugins }}')" >> $GITHUB_OUTPUT
          elif [[ "${{ inputs.version }}" == "alpha" || ${{ github.ref_name }} =~ "alpha" ]]; then
            echo "branch=$(echo 'develop')" >> $GITHUB_OUTPUT
            echo "version=$(echo 'alpha')" >> $GITHUB_OUTPUT
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.alpha-plugins }}')" >> $GITHUB_OUTPUT
          else
            echo "branch=$(echo 'main')" >> $GITHUB_OUTPUT
            echo "version=$(echo 'rc')" >> $GITHUB_OUTPUT
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.rc-plugins }}')" >> $GITHUB_OUTPUT
          fi
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: nocobase,pro-plugins,${{ join(fromJSON(steps.get-info.outputs.proRepos), ',') }}
          skip-token-revoke: true
      - name: Get GitHub App User ID
        id: get-user-id
        run: echo "user-id=$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/nocobase
          ref: ${{ steps.get-info.outputs.branch }}
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          fetch-depth: 0
      - name: Checkout pro-plugins
        uses: actions/checkout@v4
        with:
          repository: nocobase/pro-plugins
          path: packages/pro-plugins
          ref: ${{ steps.get-info.outputs.branch }}
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
      - name: Clone pro repos
        shell: bash
        run: |
          for repo in ${{ join(fromJSON(steps.get-info.outputs.proRepos), ' ') }}
          do
          git clone -b ${{ steps.get-info.outputs.branch }} https://x-access-token:${{ steps.app-token.outputs.token }}@github.com/nocobase/$repo.git packages/pro-plugins/@nocobase/$repo
          done
      - name: Set user
        run: |
          git config --global user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git config --global user.email '${{ steps.get-user-id.outputs.user-id }}+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com>'
      - name: Set Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Install dependencies
        run: yarn install
      - name: Run script
        shell: bash
        run: |
          node scripts/release/changelogAndRelease.js --ver ${{ steps.get-info.outputs.version }} --cmsURL ${{ secrets.CMS_URL }} --cmsToken ${{ secrets.CMS_TOKEN }}
        env:
          PRO_PLUGIN_REPOS: ${{ steps.get-info.outputs.proRepos }}
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Commit and push
        if: ${{ steps.get-info.outputs.version == 'rc' }}
        run: |
          git pull origin main
          git add CHANGELOG.md CHANGELOG.zh-CN.md
          git commit -m "docs: update changelogs"
          git push origin main
