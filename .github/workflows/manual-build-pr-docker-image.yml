name: Manual build pr docker image

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'Please enter a pull request number'
        required: true

jobs:
  push-acr:
    runs-on: ubuntu-latest
    services:
      verdaccio:
        image: verdaccio/verdaccio
        ports:
          - 4873:4873
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ssh-key: ${{ secrets.SUBMODULE_SSH_KEY }}
          submodules: true
      - run: gh pr checkout ${{ inputs.pr_number }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: network=host
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALI_DOCKER_REGISTRY }}
          username: ${{ secrets.ALI_DOCKER_USERNAME }}
          password: ${{ secrets.ALI_DOCKER_PASSWORD }}
      - name: Build and push - pr-${{ inputs.pr_number }}
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile.pro
          build-args: |
            VERDACCIO_URL=http://localhost:4873/
            COMMIT_HASH=${GITHUB_SHA}
          push: true
          tags: ${{ secrets.ALI_DOCKER_REGISTRY }}/nocobase/nocobase:pr-${{ inputs.pr_number }}
      - name: Deploy NocoBase
        run: |
          curl --retry 2 --location --request POST "${{secrets.NOCOBASE_DEPLOY_HOST}}pr-${{ inputs.pr_number }}" \
          --header 'Content-Type: application/json' \
          -d "{
              \"tag\": \"pr-${{ inputs.pr_number }}\",
              \"dialect\": \"postgres\"
          }"
