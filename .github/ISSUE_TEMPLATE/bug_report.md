---
name: <PERSON>ug report
about: Report a bug to help us improve. Please communicate in English, and post content in other languages to NocoBase Forum https://forum.nocobase.com/.
title: ''
labels: ''
assignees: ''

---

<!-- 
First off, thank you for reporting bugs.

Please do not clear the contents of the issue template. Items marked with * are required. Issues not filled out according to the template will be closed. 

Please communicate in English, and post content in other languages to NocoBase Forum https://forum.nocobase.com/. Non-English issues will be closed.
-->

## * Describe the bug

<!-- A clear and concise description of what the bug is. -->

## * Environment

<!-- Please view it by clicking on the ? icon in the upper right corner of the NocoBase navigation bar. -->
- NocoBase version:

<!-- [e.g. PostgreSQL 12, MySQL 8.x, SQLite] -->
- Database type and version: 
  
<!-- [e.g. MacOS, Windows] -->
- OS:

<!-- Docker, Create-nocobase-app, Git source code -->
- Deployment Methods:

<!-- If using Docker for deployment, please provide. [e.g. nocobase/nocobase:latest] -->
- Docker image version:

<!-- If using Create-nocobase-app or Git source code for deployment, please provide. -->
- NodeJS version:


## * How To Reproduce

<!-- Please describe the reproduction process in as much detail as possible. -->

## Expected behavior

<!-- A clear and concise description of what you expected to happen. -->

## Screenshots

<!-- If applicable, add screenshots to help explain your problem. -->

## Logs

<!-- If it's an API error, please provide the relevant server logs. -->
